import { useState } from "react";
import reactLogo from "./assets/react.svg";
import { invoke } from "@tauri-apps/api/core";
import "./App.css";

function App() {
  const [greetMsg, setGreetMsg] = useState("");
  const [name, setName] = useState("");

  async function greet() {
    // Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
    setGreetMsg(await invoke("greet", { name }));
  }

  return (
    <main className="container">
      <h1>Ciao! Bon jour!</h1>

      <div className="row">
        <a href="https://interjc.net" target="_blank">
          <img src="/justin.jpg" className="logo Justin" alt="Justin logo" />
        </a>
      </div>

      <div className="row">
        <a href="https://interjc.net" target="_blank">
          <h2>Visit 🪄</h2>
        </a>
      </div>
    </main>
  );
}

export default App;
