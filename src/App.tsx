import React, { useState, useEffect } from 'react';
import { ConfigProvider, Layout, theme, message } from 'antd';
import { AppProvider } from './contexts/AppContext';
import { MainLayout } from './components/Layout/MainLayout';
import { DependencyCheck } from './components/DependencyCheck/DependencyCheck';
import { checkDependencies } from './services/tauri-api';
import type { DependencyStatus } from './types';
import './App.css';

const { Content } = Layout;

function App() {
  const [dependencyStatus, setDependencyStatus] = useState<DependencyStatus | null>(null);
  const [dependenciesChecked, setDependenciesChecked] = useState(false);

  useEffect(() => {
    const checkSystemDependencies = async () => {
      try {
        const status = await checkDependencies();
        setDependencyStatus(status);
        setDependenciesChecked(true);

        if (!status.ffmpeg_available || !status.whisper_available) {
          message.warning('Some dependencies are missing. Please install them to use all features.');
        }
      } catch (error) {
        console.error('Failed to check dependencies:', error);
        message.error('Failed to check system dependencies');
        setDependenciesChecked(true);
      }
    };

    checkSystemDependencies();
  }, []);

  if (!dependenciesChecked) {
    return (
      <ConfigProvider
        theme={{
          algorithm: theme.defaultAlgorithm,
        }}
      >
        <Layout style={{ minHeight: '100vh' }}>
          <Content style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <div>Checking system dependencies...</div>
          </Content>
        </Layout>
      </ConfigProvider>
    );
  }

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        token: {
          colorPrimary: '#1890ff',
        },
      }}
    >
      <AppProvider>
        {dependencyStatus && (!dependencyStatus.ffmpeg_available || !dependencyStatus.whisper_available) ? (
          <DependencyCheck status={dependencyStatus} />
        ) : (
          <MainLayout />
        )}
      </AppProvider>
    </ConfigProvider>
  );
}

export default App;
