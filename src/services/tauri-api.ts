import { invoke } from '@tauri-apps/api/core';
import { open } from '@tauri-apps/plugin-dialog';
import type {
  VideoFile,
  AudioFile,
  SubtitleData,
  AIContent,
  AIConfig,
  UserSettings,
  ProcessingHistoryItem,
  DependencyStatus,
  AppDirectories,
  AudioQualityOption,
  LanguageOption,
  ContentTypeOption,
  AudioSizeEstimate,
} from '../types';

// File handling
export const selectVideoFile = async (): Promise<VideoFile | null> => {
  try {
    const selected = await open({
      multiple: false,
      filters: [{
        name: 'Video Files',
        extensions: ['mp4', 'mov', 'avi', 'mkv', 'wmv', 'flv', 'webm', 'm4v', '3gp']
      }]
    });

    if (selected && typeof selected === 'string') {
      return await getVideoInfo(selected);
    }
    return null;
  } catch (error) {
    console.error('Error selecting video file:', error);
    throw error;
  }
};

export const validateVideoFile = async (path: string): Promise<boolean> => {
  return await invoke('validate_video_file', { path });
};

export const getVideoInfo = async (path: string): Promise<VideoFile> => {
  return await invoke('get_video_info', { path });
};

export const getSupportedFormats = async (): Promise<string[]> => {
  return await invoke('get_supported_formats');
};

export const checkDependencies = async (): Promise<DependencyStatus> => {
  return await invoke('check_dependencies');
};

// Audio extraction
export const extractAudioFromVideo = async (
  videoPath: string,
  quality: string
): Promise<AudioFile> => {
  return await invoke('extract_audio_from_video', { videoPath, quality });
};

export const getAudioQualityOptions = async (): Promise<AudioQualityOption[]> => {
  return await invoke('get_audio_quality_options');
};

export const estimateAudioSize = async (
  videoPath: string,
  quality: string
): Promise<AudioSizeEstimate> => {
  return await invoke('estimate_audio_size', { videoPath, quality });
};

export const cleanupTempAudioFiles = async (): Promise<void> => {
  return await invoke('cleanup_temp_audio_files');
};

// Speech recognition
export const transcribeAudio = async (
  audioPath: string,
  language: string,
  model: string
): Promise<SubtitleData> => {
  return await invoke('transcribe_audio', { audioPath, language, model });
};

export const getWhisperModels = async (): Promise<string[]> => {
  return await invoke('get_whisper_models');
};

export const getSupportedLanguages = async (): Promise<LanguageOption[]> => {
  return await invoke('get_supported_languages');
};

// AI content generation
export const generateAIContent = async (
  subtitleText: string,
  contentType: string,
  aiConfig: AIConfig
): Promise<AIContent> => {
  return await invoke('generate_ai_content', { subtitleText, contentType, aiConfig });
};

export const testAIConnection = async (aiConfig: AIConfig): Promise<boolean> => {
  return await invoke('test_ai_connection', { aiConfig });
};

export const getContentTypes = async (): Promise<ContentTypeOption[]> => {
  return await invoke('get_content_types');
};

// Configuration management
export const saveUserSettings = async (settings: UserSettings): Promise<void> => {
  return await invoke('save_user_settings', { settings });
};

export const loadUserSettings = async (): Promise<UserSettings> => {
  return await invoke('load_user_settings');
};

export const deleteApiKey = async (): Promise<void> => {
  return await invoke('delete_api_key');
};

// History management
export const getProcessingHistory = async (): Promise<ProcessingHistoryItem[]> => {
  return await invoke('get_processing_history');
};

export const saveProcessingHistoryItem = async (
  historyItem: ProcessingHistoryItem
): Promise<void> => {
  return await invoke('save_processing_history_item', { historyItem });
};

// App directories
export const getAppDirectories = async (): Promise<AppDirectories> => {
  return await invoke('get_app_directories');
};

// Utility functions
export const formatFileSize = (bytes: number): string => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
};

export const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};
