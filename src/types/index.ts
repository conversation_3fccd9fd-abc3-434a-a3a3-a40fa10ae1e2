// Video and Audio types
export interface VideoFile {
  id: string;
  path: string;
  name: string;
  size: number;
  duration?: number;
  format: string;
  created_at: string;
}

export interface AudioFile {
  id: string;
  path: string;
  name: string;
  size: number;
  duration: number;
  sample_rate: number;
  bitrate: number;
  channels: number;
  created_at: string;
}

// Subtitle types
export interface SubtitleData {
  id: string;
  entries: SubtitleEntry[];
  language: string;
  total_duration: number;
  created_at: string;
}

export interface SubtitleEntry {
  index: number;
  start_time: number;
  end_time: number;
  text: string;
}

// AI Content types
export interface AIContent {
  id: string;
  seo_content: SEOContent;
  youtube_guide: YouTubeGuide;
  cover_suggestions: CoverSuggestion[];
  created_at: string;
}

export interface SEOContent {
  keywords: string[];
  meta_description: string;
  title_suggestions: string[];
  content_summary: string;
}

export interface YouTubeGuide {
  title: string;
  description: string;
  tags: string[];
  thumbnail_tips: string[];
  best_posting_time: string;
  target_audience: string;
}

export interface CoverSuggestion {
  title: string;
  description: string;
  style: string;
  color_scheme: string[];
  elements: string[];
}

// Configuration types
export interface UserSettings {
  ai_config: AIConfig;
  audio_quality: string;
  language: string;
  theme: string;
  output_directory: string;
}

export interface AIConfig {
  base_url: string;
  api_key: string;
  model_name: string;
  temperature: number;
  max_tokens: number;
}

// Processing types
export interface ProcessingStatus {
  stage: ProcessingStage;
  progress: number;
  message: string;
  current_file?: string;
}

export type ProcessingStage = 
  | 'idle' 
  | 'validating' 
  | 'extracting' 
  | 'transcribing' 
  | 'generating' 
  | 'complete' 
  | 'error';

// UI Option types
export interface AudioQualityOption {
  value: string;
  label: string;
  description: string;
}

export interface LanguageOption {
  code: string;
  name: string;
}

export interface ContentTypeOption {
  value: string;
  label: string;
  description: string;
}

// History types
export interface ProcessingHistoryItem {
  id: string;
  video_file_name: string;
  video_file_path: string;
  processing_time: number;
  created_at: string;
  status: string;
  output_files: string[];
}

// Dependency status
export interface DependencyStatus {
  ffmpeg_available: boolean;
  whisper_available: boolean;
  ffmpeg_version?: string;
  whisper_version?: string;
}

// App directories
export interface AppDirectories {
  temp_dir: string;
  output_dir: string;
}

// Audio size estimate
export interface AudioSizeEstimate {
  estimated_size_bytes: number;
  estimated_size_mb: number;
  duration_seconds: number;
  bitrate_kbps: number;
}
