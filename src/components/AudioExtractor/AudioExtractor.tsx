import React, { useState, useEffect } from 'react';
import { 
  Button, 
  Space, 
  Typography, 
  Card, 
  Select, 
  Descriptions, 
  Alert,
  message 
} from 'antd';
import { 
  AudioOutlined, 
  CheckCircleOutlined,
  PlayCircleOutlined 
} from '@ant-design/icons';
import { 
  extractAudioFromVideo, 
  getAudioQualityOptions, 
  estimateAudioSize,
  formatFileSize,
  formatDuration 
} from '../../services/tauri-api';
import { useApp } from '../../contexts/AppContext';
import type { AudioQualityOption, AudioSizeEstimate } from '../../types';

const { Title, Text } = Typography;
const { Option } = Select;

interface AudioExtractorProps {
  onNext: () => void;
}

export const AudioExtractor: React.FC<AudioExtractorProps> = ({ onNext }) => {
  const { state, actions } = useApp();
  const [qualityOptions, setQualityOptions] = useState<AudioQualityOption[]>([]);
  const [selectedQuality, setSelectedQuality] = useState<string>('192');
  const [sizeEstimate, setSizeEstimate] = useState<AudioSizeEstimate | null>(null);
  const [isExtracting, setIsExtracting] = useState(false);

  useEffect(() => {
    const loadQualityOptions = async () => {
      try {
        const options = await getAudioQualityOptions();
        setQualityOptions(options);
        
        // Set default quality from settings
        if (state.settings.audio_quality) {
          setSelectedQuality(state.settings.audio_quality);
        }
      } catch (error) {
        console.error('Failed to load quality options:', error);
        message.error('Failed to load audio quality options');
      }
    };

    loadQualityOptions();
  }, [state.settings.audio_quality]);

  useEffect(() => {
    const updateSizeEstimate = async () => {
      if (state.currentVideo && selectedQuality) {
        try {
          const estimate = await estimateAudioSize(state.currentVideo.path, selectedQuality);
          setSizeEstimate(estimate);
        } catch (error) {
          console.error('Failed to estimate audio size:', error);
        }
      }
    };

    updateSizeEstimate();
  }, [state.currentVideo, selectedQuality]);

  const handleExtractAudio = async () => {
    if (!state.currentVideo) {
      message.error('No video file selected');
      return;
    }

    try {
      setIsExtracting(true);
      actions.updateProcessingStatus({
        stage: 'extracting',
        progress: 0,
        message: 'Starting audio extraction...',
      });

      const audioFile = await extractAudioFromVideo(
        state.currentVideo.path,
        selectedQuality
      );

      actions.setCurrentAudio(audioFile);
      actions.updateProcessingStatus({
        stage: 'idle',
        progress: 100,
        message: 'Audio extraction completed',
      });

      message.success('Audio extracted successfully');
    } catch (error) {
      console.error('Audio extraction failed:', error);
      message.error('Failed to extract audio');
      actions.updateProcessingStatus({
        stage: 'error',
        progress: 0,
        message: 'Audio extraction failed',
      });
    } finally {
      setIsExtracting(false);
    }
  };

  const handleQualityChange = (quality: string) => {
    setSelectedQuality(quality);
    // Update settings
    actions.updateSettings({
      audio_quality: quality,
    });
  };

  if (state.currentAudio) {
    return (
      <div>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <CheckCircleOutlined 
              style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} 
            />
            <Title level={4}>Audio Extracted Successfully</Title>
          </div>

          <Card>
            <Descriptions title="Audio Information" bordered column={1}>
              <Descriptions.Item label="File Name">
                {state.currentAudio.name}
              </Descriptions.Item>
              <Descriptions.Item label="File Size">
                {formatFileSize(state.currentAudio.size)}
              </Descriptions.Item>
              <Descriptions.Item label="Duration">
                {formatDuration(state.currentAudio.duration)}
              </Descriptions.Item>
              <Descriptions.Item label="Bitrate">
                {state.currentAudio.bitrate} kbps
              </Descriptions.Item>
              <Descriptions.Item label="Sample Rate">
                {state.currentAudio.sample_rate} Hz
              </Descriptions.Item>
              <Descriptions.Item label="Channels">
                {state.currentAudio.channels === 1 ? 'Mono' : 'Stereo'}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          <div style={{ textAlign: 'center' }}>
            <Space>
              <Button 
                icon={<AudioOutlined />}
                onClick={() => actions.setCurrentAudio(null)}
              >
                Extract Again
              </Button>
              <Button type="primary" onClick={onNext}>
                Continue to Speech Recognition
              </Button>
            </Space>
          </div>
        </Space>
      </div>
    );
  }

  if (!state.currentVideo) {
    return (
      <Alert
        message="No Video File Selected"
        description="Please select a video file first before extracting audio."
        type="warning"
        showIcon
      />
    );
  }

  return (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div style={{ textAlign: 'center' }}>
          <AudioOutlined 
            style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} 
          />
          <Title level={4}>Extract Audio from Video</Title>
          <Text type="secondary">
            Configure audio quality and extract audio from your video file
          </Text>
        </div>

        <Card title="Audio Quality Settings">
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <div>
              <Text strong>Quality:</Text>
              <Select
                value={selectedQuality}
                onChange={handleQualityChange}
                style={{ width: '100%', marginTop: '8px' }}
                size="large"
              >
                {qualityOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    <div>
                      <div>{option.label}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {option.description}
                      </div>
                    </div>
                  </Option>
                ))}
              </Select>
            </div>

            {sizeEstimate && (
              <Card size="small" style={{ background: '#f6ffed' }}>
                <Descriptions size="small" column={2}>
                  <Descriptions.Item label="Estimated Size">
                    {formatFileSize(sizeEstimate.estimated_size_bytes)}
                  </Descriptions.Item>
                  <Descriptions.Item label="Bitrate">
                    {sizeEstimate.bitrate_kbps} kbps
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            )}
          </Space>
        </Card>

        <Card title="Source Video Information">
          <Descriptions column={1}>
            <Descriptions.Item label="File Name">
              {state.currentVideo.name}
            </Descriptions.Item>
            <Descriptions.Item label="File Size">
              {formatFileSize(state.currentVideo.size)}
            </Descriptions.Item>
            {state.currentVideo.duration && (
              <Descriptions.Item label="Duration">
                {formatDuration(state.currentVideo.duration)}
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>

        <Alert
          message="Audio Extraction Process"
          description="This process will extract the audio track from your video file and convert it to MP3 format. The extraction time depends on the video length and selected quality."
          type="info"
          showIcon
        />

        <div style={{ textAlign: 'center' }}>
          <Button 
            type="primary" 
            size="large"
            icon={<PlayCircleOutlined />}
            onClick={handleExtractAudio}
            loading={isExtracting}
          >
            {isExtracting ? 'Extracting Audio...' : 'Extract Audio'}
          </Button>
        </div>
      </Space>
    </div>
  );
};
