import React, { useState } from 'react';
import { Upload, Button, Space, Typography, Card, Descriptions, message } from 'antd';
import { 
  InboxOutlined, 
  VideoCameraOutlined, 
  CheckCircleOutlined,
  LoadingOutlined 
} from '@ant-design/icons';
import { selectVideoFile, validateVideoFile, formatFileSize, formatDuration } from '../../services/tauri-api';
import { useApp } from '../../contexts/AppContext';
import type { VideoFile } from '../../types';

const { Dragger } = Upload;
const { Title, Text } = Typography;

interface FileUploaderProps {
  onNext: () => void;
}

export const FileUploader: React.FC<FileUploaderProps> = ({ onNext }) => {
  const { state, actions } = useApp();
  const [isValidating, setIsValidating] = useState(false);

  const handleFileSelect = async () => {
    try {
      setIsValidating(true);
      actions.updateProcessingStatus({
        stage: 'validating',
        progress: 0,
        message: 'Opening file dialog...',
      });

      const videoFile = await selectVideoFile();
      
      if (videoFile) {
        actions.updateProcessingStatus({
          stage: 'validating',
          progress: 50,
          message: 'Validating video file...',
        });

        const isValid = await validateVideoFile(videoFile.path);
        
        if (isValid) {
          actions.setCurrentVideo(videoFile);
          actions.updateProcessingStatus({
            stage: 'idle',
            progress: 100,
            message: 'Video file loaded successfully',
          });
          message.success('Video file loaded successfully');
        } else {
          message.error('Invalid video file or unsupported format');
          actions.updateProcessingStatus({
            stage: 'error',
            progress: 0,
            message: 'Invalid video file',
          });
        }
      } else {
        actions.updateProcessingStatus({
          stage: 'idle',
          progress: 0,
          message: 'No file selected',
        });
      }
    } catch (error) {
      console.error('Error selecting video file:', error);
      message.error('Failed to select video file');
      actions.updateProcessingStatus({
        stage: 'error',
        progress: 0,
        message: 'Failed to select video file',
      });
    } finally {
      setIsValidating(false);
    }
  };

  const handleRemoveFile = () => {
    actions.setCurrentVideo(null);
    actions.updateProcessingStatus({
      stage: 'idle',
      progress: 0,
      message: 'Ready',
    });
  };

  if (state.currentVideo) {
    return (
      <div>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <CheckCircleOutlined 
              style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} 
            />
            <Title level={4}>Video File Selected</Title>
          </div>

          <Card>
            <Descriptions title="Video Information" bordered column={1}>
              <Descriptions.Item label="File Name">
                {state.currentVideo.name}
              </Descriptions.Item>
              <Descriptions.Item label="File Size">
                {formatFileSize(state.currentVideo.size)}
              </Descriptions.Item>
              {state.currentVideo.duration && (
                <Descriptions.Item label="Duration">
                  {formatDuration(state.currentVideo.duration)}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="Format">
                {state.currentVideo.format || 'Unknown'}
              </Descriptions.Item>
              <Descriptions.Item label="Path">
                <Text code style={{ fontSize: '12px' }}>
                  {state.currentVideo.path}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>

          <div style={{ textAlign: 'center' }}>
            <Space>
              <Button onClick={handleRemoveFile}>
                Select Different File
              </Button>
              <Button type="primary" onClick={onNext}>
                Continue to Audio Extraction
              </Button>
            </Space>
          </div>
        </Space>
      </div>
    );
  }

  return (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div style={{ textAlign: 'center' }}>
          <VideoCameraOutlined 
            style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} 
          />
          <Title level={4}>Select Video File</Title>
          <Text type="secondary">
            Choose a video file to extract audio and generate subtitles
          </Text>
        </div>

        <Dragger
          name="video"
          multiple={false}
          showUploadList={false}
          beforeUpload={() => false} // Prevent automatic upload
          accept="video/*"
          style={{ padding: '40px 20px' }}
          disabled={isValidating}
        >
          <p className="ant-upload-drag-icon">
            {isValidating ? (
              <LoadingOutlined style={{ fontSize: '48px' }} />
            ) : (
              <InboxOutlined style={{ fontSize: '48px' }} />
            )}
          </p>
          <p className="ant-upload-text">
            {isValidating ? 'Validating file...' : 'Click or drag video file to this area'}
          </p>
          <p className="ant-upload-hint">
            Supports MP4, MOV, AVI, MKV, WMV, FLV, WebM, M4V, 3GP formats
          </p>
        </Dragger>

        <div style={{ textAlign: 'center' }}>
          <Button 
            type="primary" 
            size="large"
            onClick={handleFileSelect}
            loading={isValidating}
            icon={<VideoCameraOutlined />}
          >
            {isValidating ? 'Validating...' : 'Browse for Video File'}
          </Button>
        </div>

        <Card size="small" style={{ background: '#f6ffed', border: '1px solid #b7eb8f' }}>
          <Space direction="vertical" size="small">
            <Text strong style={{ color: '#52c41a' }}>Supported Formats:</Text>
            <Text style={{ fontSize: '12px' }}>
              MP4, MOV, AVI, MKV, WMV, FLV, WebM, M4V, 3GP
            </Text>
            <Text strong style={{ color: '#52c41a' }}>Requirements:</Text>
            <Text style={{ fontSize: '12px' }}>
              • FFmpeg must be installed for video processing
              <br />
              • Whisper must be installed for speech recognition
            </Text>
          </Space>
        </Card>
      </Space>
    </div>
  );
};
