import React, { useState, useEffect } from 'react';
import { 
  Button, 
  Space, 
  Typography, 
  Card, 
  Select, 
  Alert,
  message,
  Checkbox,
  Descriptions
} from 'antd';
import { 
  BulbOutlined, 
  CheckCircleOutlined,
  PlayCircleOutlined,
  SettingOutlined 
} from '@ant-design/icons';
import { 
  generateAIContent, 
  getContentTypes,
  testAIConnection 
} from '../../services/tauri-api';
import { useApp } from '../../contexts/AppContext';
import type { ContentTypeOption } from '../../types';

const { Title, Text } = Typography;
const { Option } = Select;

interface AIContentGeneratorProps {
  onNext: () => void;
}

export const AIContentGenerator: React.FC<AIContentGeneratorProps> = ({ onNext }) => {
  const { state, actions } = useApp();
  const [contentTypes, setContentTypes] = useState<ContentTypeOption[]>([]);
  const [selectedTypes, setSelectedTypes] = useState<string[]>(['all']);
  const [isGenerating, setIsGenerating] = useState(false);
  const [apiConfigured, setApiConfigured] = useState(false);

  useEffect(() => {
    const loadOptions = async () => {
      try {
        const types = await getContentTypes();
        setContentTypes(types);
      } catch (error) {
        console.error('Failed to load content types:', error);
        message.error('Failed to load content type options');
      }
    };

    loadOptions();
  }, []);

  useEffect(() => {
    // Check if AI API is configured
    const checkApiConfig = () => {
      const hasApiKey = state.settings.ai_config.api_key.length > 0;
      const hasBaseUrl = state.settings.ai_config.base_url.length > 0;
      const hasModel = state.settings.ai_config.model_name.length > 0;
      
      setApiConfigured(hasApiKey && hasBaseUrl && hasModel);
    };

    checkApiConfig();
  }, [state.settings.ai_config]);

  const handleGenerate = async () => {
    if (!state.currentSubtitle) {
      message.error('No subtitle data available');
      return;
    }

    if (!apiConfigured) {
      message.error('Please configure AI API settings first');
      return;
    }

    try {
      setIsGenerating(true);
      actions.updateProcessingStatus({
        stage: 'generating',
        progress: 0,
        message: 'Starting AI content generation...',
      });

      // Convert subtitle entries to text
      const subtitleText = state.currentSubtitle.entries
        .map(entry => entry.text)
        .join(' ');

      const contentType = selectedTypes.includes('all') ? 'all' : selectedTypes[0];

      const aiContent = await generateAIContent(
        subtitleText,
        contentType,
        state.settings.ai_config
      );

      actions.setCurrentAIContent(aiContent);
      actions.updateProcessingStatus({
        stage: 'complete',
        progress: 100,
        message: 'AI content generation completed',
      });

      message.success('AI content generated successfully');
    } catch (error) {
      console.error('AI content generation failed:', error);
      message.error('Failed to generate AI content');
      actions.updateProcessingStatus({
        stage: 'error',
        progress: 0,
        message: 'AI content generation failed',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      const success = await testAIConnection(state.settings.ai_config);
      if (success) {
        message.success('AI API connection successful');
        setApiConfigured(true);
      } else {
        message.error('AI API connection failed');
        setApiConfigured(false);
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      message.error('Connection test failed');
      setApiConfigured(false);
    }
  };

  if (state.currentAIContent) {
    return (
      <div>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <CheckCircleOutlined 
              style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} 
            />
            <Title level={4}>AI Content Generated Successfully</Title>
          </div>

          <Card>
            <Descriptions title="Generated Content Summary" bordered column={1}>
              <Descriptions.Item label="SEO Keywords">
                {state.currentAIContent.seo_content.keywords.length} keywords
              </Descriptions.Item>
              <Descriptions.Item label="Title Suggestions">
                {state.currentAIContent.seo_content.title_suggestions.length} suggestions
              </Descriptions.Item>
              <Descriptions.Item label="YouTube Tags">
                {state.currentAIContent.youtube_guide.tags.length} tags
              </Descriptions.Item>
              <Descriptions.Item label="Cover Suggestions">
                {state.currentAIContent.cover_suggestions.length} designs
              </Descriptions.Item>
              <Descriptions.Item label="Generated At">
                {new Date(state.currentAIContent.created_at).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          <Card title="Quick Preview" size="small">
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div>
                <Text strong>YouTube Title:</Text>
                <br />
                <Text>{state.currentAIContent.youtube_guide.title}</Text>
              </div>
              <div>
                <Text strong>Meta Description:</Text>
                <br />
                <Text>{state.currentAIContent.seo_content.meta_description}</Text>
              </div>
            </Space>
          </Card>

          <div style={{ textAlign: 'center' }}>
            <Space>
              <Button 
                icon={<BulbOutlined />}
                onClick={() => actions.setCurrentAIContent(null)}
              >
                Generate Again
              </Button>
              <Button type="primary" onClick={onNext}>
                View Full Results
              </Button>
            </Space>
          </div>
        </Space>
      </div>
    );
  }

  if (!state.currentSubtitle) {
    return (
      <Alert
        message="No Subtitle Data Available"
        description="Please generate subtitles first before creating AI content."
        type="warning"
        showIcon
      />
    );
  }

  return (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div style={{ textAlign: 'center' }}>
          <BulbOutlined 
            style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} 
          />
          <Title level={4}>Generate AI Content</Title>
          <Text type="secondary">
            Create SEO content and YouTube optimization suggestions using AI
          </Text>
        </div>

        {!apiConfigured && (
          <Alert
            message="AI API Configuration Required"
            description={
              <div>
                <p>Please configure your AI API settings to generate content.</p>
                <Button 
                  type="link" 
                  icon={<SettingOutlined />}
                  onClick={() => message.info('Please go to Settings to configure AI API')}
                >
                  Go to Settings
                </Button>
                <Button 
                  type="link" 
                  onClick={handleTestConnection}
                >
                  Test Current Configuration
                </Button>
              </div>
            }
            type="warning"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        <Card title="Content Generation Settings">
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <div>
              <Text strong>Content Types to Generate:</Text>
              <Checkbox.Group
                value={selectedTypes}
                onChange={setSelectedTypes}
                style={{ width: '100%', marginTop: '8px' }}
              >
                <Space direction="vertical" size="small">
                  {contentTypes.map((type) => (
                    <Checkbox key={type.value} value={type.value}>
                      <div>
                        <Text strong>{type.label}</Text>
                        <br />
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {type.description}
                        </Text>
                      </div>
                    </Checkbox>
                  ))}
                </Space>
              </Checkbox.Group>
            </div>

            <div>
              <Text strong>AI Model Configuration:</Text>
              <Card size="small" style={{ marginTop: '8px', background: '#f5f5f5' }}>
                <Descriptions size="small" column={1}>
                  <Descriptions.Item label="Model">
                    {state.settings.ai_config.model_name}
                  </Descriptions.Item>
                  <Descriptions.Item label="Temperature">
                    {state.settings.ai_config.temperature}
                  </Descriptions.Item>
                  <Descriptions.Item label="Max Tokens">
                    {state.settings.ai_config.max_tokens}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
            </div>
          </Space>
        </Card>

        <Card title="Source Content Information">
          <Descriptions column={1}>
            <Descriptions.Item label="Subtitle Entries">
              {state.currentSubtitle.entries.length} entries
            </Descriptions.Item>
            <Descriptions.Item label="Total Text Length">
              {state.currentSubtitle.entries.reduce((acc, entry) => acc + entry.text.length, 0)} characters
            </Descriptions.Item>
            <Descriptions.Item label="Language">
              {state.currentSubtitle.language}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Alert
          message="AI Content Generation"
          description={
            <div>
              <p>This process will analyze your subtitle content and generate:</p>
              <ul>
                <li><strong>SEO Content:</strong> Keywords, meta descriptions, and title suggestions</li>
                <li><strong>YouTube Guide:</strong> Optimized titles, descriptions, and tags</li>
                <li><strong>Cover Suggestions:</strong> Design ideas for thumbnails and covers</li>
              </ul>
              <p><strong>Note:</strong> Generation time depends on content length and AI model response time.</p>
            </div>
          }
          type="info"
          showIcon
        />

        <div style={{ textAlign: 'center' }}>
          <Button 
            type="primary" 
            size="large"
            icon={<PlayCircleOutlined />}
            onClick={handleGenerate}
            loading={isGenerating}
            disabled={!apiConfigured || selectedTypes.length === 0}
          >
            {isGenerating ? 'Generating AI Content...' : 'Generate AI Content'}
          </Button>
        </div>
      </Space>
    </div>
  );
};
