import React, { useState } from 'react';
import { Row, Col, Card, Steps, Button, Space, message } from 'antd';
import { 
  VideoCameraOutlined, 
  AudioOutlined, 
  FileTextOutlined, 
  BulbOutlined 
} from '@ant-design/icons';
import { FileUploader } from '../FileUploader/FileUploader';
import { AudioExtractor } from '../AudioExtractor/AudioExtractor';
import { SpeechRecognizer } from '../SpeechRecognizer/SpeechRecognizer';
import { AIContentGenerator } from '../AIContentGenerator/AIContentGenerator';
import { ProcessingProgress } from '../ProcessingProgress/ProcessingProgress';
import { useApp } from '../../contexts/AppContext';

const { Step } = Steps;

export const HomePage: React.FC = () => {
  const { state, actions } = useApp();
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: 'Select Video',
      description: 'Choose a video file to process',
      icon: <VideoCameraOutlined />,
      component: <FileUploader onNext={() => setCurrentStep(1)} />,
      completed: !!state.currentVideo,
    },
    {
      title: 'Extract Audio',
      description: 'Extract audio from the video',
      icon: <AudioOutlined />,
      component: <AudioExtractor onNext={() => setCurrentStep(2)} />,
      completed: !!state.currentAudio,
    },
    {
      title: 'Generate Subtitles',
      description: 'Convert speech to text',
      icon: <FileTextOutlined />,
      component: <SpeechRecognizer onNext={() => setCurrentStep(3)} />,
      completed: !!state.currentSubtitle,
    },
    {
      title: 'AI Content',
      description: 'Generate SEO and YouTube content',
      icon: <BulbOutlined />,
      component: <AIContentGenerator onNext={() => message.success('Processing complete!')} />,
      completed: !!state.currentAIContent,
    },
  ];

  const handleStepClick = (step: number) => {
    // Only allow going to previous steps or the next logical step
    if (step <= currentStep || (step === currentStep + 1 && steps[currentStep].completed)) {
      setCurrentStep(step);
    }
  };

  const handleReset = () => {
    actions.resetCurrentFiles();
    setCurrentStep(0);
  };

  // Determine current step based on completed steps
  React.useEffect(() => {
    if (state.currentAIContent) {
      setCurrentStep(3);
    } else if (state.currentSubtitle) {
      setCurrentStep(3);
    } else if (state.currentAudio) {
      setCurrentStep(2);
    } else if (state.currentVideo) {
      setCurrentStep(1);
    } else {
      setCurrentStep(0);
    }
  }, [state.currentVideo, state.currentAudio, state.currentSubtitle, state.currentAIContent]);

  return (
    <div>
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Card>
            <div style={{ marginBottom: '24px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <h3 style={{ margin: 0 }}>Processing Steps</h3>
              {(state.currentVideo || state.currentAudio || state.currentSubtitle || state.currentAIContent) && (
                <Button type="text" danger onClick={handleReset}>
                  Start Over
                </Button>
              )}
            </div>
            
            <Steps 
              current={currentStep} 
              onChange={handleStepClick}
              type="navigation"
              size="small"
            >
              {steps.map((step, index) => (
                <Step
                  key={index}
                  title={step.title}
                  description={step.description}
                  icon={step.icon}
                  status={
                    step.completed 
                      ? 'finish' 
                      : index === currentStep 
                        ? 'process' 
                        : 'wait'
                  }
                />
              ))}
            </Steps>
          </Card>
        </Col>

        <Col span={24}>
          {state.processingStatus.stage !== 'idle' && (
            <Card style={{ marginBottom: '24px' }}>
              <ProcessingProgress />
            </Card>
          )}
        </Col>

        <Col span={24}>
          <Card>
            {steps[currentStep]?.component}
          </Card>
        </Col>
      </Row>

      {/* Quick Status Overview */}
      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <VideoCameraOutlined 
                style={{ 
                  fontSize: '24px', 
                  color: state.currentVideo ? '#52c41a' : '#d9d9d9',
                  marginBottom: '8px'
                }} 
              />
              <div style={{ fontSize: '12px', color: '#666' }}>
                Video: {state.currentVideo ? 'Selected' : 'Not selected'}
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <AudioOutlined 
                style={{ 
                  fontSize: '24px', 
                  color: state.currentAudio ? '#52c41a' : '#d9d9d9',
                  marginBottom: '8px'
                }} 
              />
              <div style={{ fontSize: '12px', color: '#666' }}>
                Audio: {state.currentAudio ? 'Extracted' : 'Not extracted'}
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <FileTextOutlined 
                style={{ 
                  fontSize: '24px', 
                  color: state.currentSubtitle ? '#52c41a' : '#d9d9d9',
                  marginBottom: '8px'
                }} 
              />
              <div style={{ fontSize: '12px', color: '#666' }}>
                Subtitles: {state.currentSubtitle ? 'Generated' : 'Not generated'}
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card size="small">
            <div style={{ textAlign: 'center' }}>
              <BulbOutlined 
                style={{ 
                  fontSize: '24px', 
                  color: state.currentAIContent ? '#52c41a' : '#d9d9d9',
                  marginBottom: '8px'
                }} 
              />
              <div style={{ fontSize: '12px', color: '#666' }}>
                AI Content: {state.currentAIContent ? 'Generated' : 'Not generated'}
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};
