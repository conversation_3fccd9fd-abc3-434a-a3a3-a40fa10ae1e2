import React, { useState } from 'react';
import { Layout, Menu, Typography, Button, Space } from 'antd';
import {
  HomeOutlined,
  SettingOutlined,
  HistoryOutlined,
  FileTextOutlined,
  BulbOutlined,
} from '@ant-design/icons';
import { HomePage } from '../Pages/HomePage';
import { SettingsPage } from '../Pages/SettingsPage';
import { HistoryPage } from '../Pages/HistoryPage';
import { ResultsPage } from '../Pages/ResultsPage';
import { useApp } from '../../contexts/AppContext';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

type PageKey = 'home' | 'settings' | 'history' | 'results';

export const MainLayout: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<PageKey>('home');
  const [collapsed, setCollapsed] = useState(false);
  const { state, actions } = useApp();

  const menuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: 'Home',
    },
    {
      key: 'results',
      icon: <FileTextOutlined />,
      label: 'Results',
      disabled: !state.currentSubtitle && !state.currentAIContent,
    },
    {
      key: 'history',
      icon: <HistoryOutlined />,
      label: 'History',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
  ];

  const renderContent = () => {
    switch (currentPage) {
      case 'home':
        return <HomePage />;
      case 'settings':
        return <SettingsPage />;
      case 'history':
        return <HistoryPage />;
      case 'results':
        return <ResultsPage />;
      default:
        return <HomePage />;
    }
  };

  const getPageTitle = () => {
    switch (currentPage) {
      case 'home':
        return 'Video Processing';
      case 'settings':
        return 'Settings';
      case 'history':
        return 'Processing History';
      case 'results':
        return 'Results';
      default:
        return 'Speech Extract';
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        theme="light"
        width={250}
      >
        <div style={{ 
          padding: '16px', 
          textAlign: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <BulbOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          {!collapsed && (
            <Title level={4} style={{ margin: '8px 0 0 0', color: '#1890ff' }}>
              Speech Extract
            </Title>
          )}
        </div>
        
        <Menu
          mode="inline"
          selectedKeys={[currentPage]}
          items={menuItems}
          onClick={({ key }) => setCurrentPage(key as PageKey)}
          style={{ border: 'none' }}
        />
      </Sider>

      <Layout>
        <Header style={{ 
          background: '#fff', 
          padding: '0 24px',
          borderBottom: '1px solid #f0f0f0',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Title level={3} style={{ margin: 0 }}>
            {getPageTitle()}
          </Title>
          
          <Space>
            {state.processingStatus.stage !== 'idle' && (
              <div style={{ 
                padding: '4px 12px',
                background: '#f6ffed',
                border: '1px solid #b7eb8f',
                borderRadius: '6px',
                fontSize: '12px',
                color: '#52c41a'
              }}>
                {state.processingStatus.message}
              </div>
            )}
            
            {(state.currentVideo || state.currentAudio || state.currentSubtitle) && (
              <Button 
                type="text" 
                danger
                onClick={actions.resetCurrentFiles}
                size="small"
              >
                Clear All
              </Button>
            )}
          </Space>
        </Header>

        <Content style={{ 
          margin: '24px',
          padding: '24px',
          background: '#fff',
          borderRadius: '8px',
          minHeight: 'calc(100vh - 112px)'
        }}>
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  );
};
