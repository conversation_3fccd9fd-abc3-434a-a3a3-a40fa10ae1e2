import React from 'react';
import { Layout, Card, Alert, Typography, Space, Button, List, Tag } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  ExclamationCircleOutlined,
  ReloadOutlined 
} from '@ant-design/icons';
import type { DependencyStatus } from '../../types';

const { Content } = Layout;
const { Title, Paragraph, Text } = Typography;

interface DependencyCheckProps {
  status: DependencyStatus;
}

export const DependencyCheck: React.FC<DependencyCheckProps> = ({ status }) => {
  const handleRefresh = () => {
    window.location.reload();
  };

  const installInstructions = [
    {
      name: 'FFmpeg',
      missing: !status.ffmpeg_available,
      instructions: [
        'Install via Homebrew: brew install ffmpeg',
        'Or download from: https://ffmpeg.org/download.html',
        'Make sure ffmpeg is in your PATH'
      ]
    },
    {
      name: 'Whisper',
      missing: !status.whisper_available,
      instructions: [
        'Install via pip: pip install openai-whisper',
        'Or via conda: conda install -c conda-forge openai-whisper',
        'Make sure whisper command is available in your PATH'
      ]
    }
  ];

  const missingDependencies = installInstructions.filter(dep => dep.missing);

  return (
    <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      <Content style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        padding: '24px'
      }}>
        <Card 
          style={{ 
            maxWidth: 800, 
            width: '100%',
            boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
          }}
        >
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div style={{ textAlign: 'center' }}>
              <ExclamationCircleOutlined 
                style={{ 
                  fontSize: '48px', 
                  color: '#faad14',
                  marginBottom: '16px'
                }} 
              />
              <Title level={2}>System Dependencies Required</Title>
              <Paragraph type="secondary">
                Speech Extract requires external tools to process video and audio files.
                Please install the missing dependencies to continue.
              </Paragraph>
            </div>

            <Alert
              message="Missing Dependencies Detected"
              description={`${missingDependencies.length} required ${missingDependencies.length === 1 ? 'dependency is' : 'dependencies are'} missing.`}
              type="warning"
              showIcon
            />

            <div>
              <Title level={4}>Dependency Status</Title>
              <List
                dataSource={[
                  {
                    name: 'FFmpeg',
                    available: status.ffmpeg_available,
                    version: status.ffmpeg_version,
                    description: 'Required for video processing and audio extraction'
                  },
                  {
                    name: 'Whisper',
                    available: status.whisper_available,
                    version: status.whisper_version,
                    description: 'Required for speech recognition and subtitle generation'
                  }
                ]}
                renderItem={(item) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        item.available ? (
                          <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '20px' }} />
                        ) : (
                          <CloseCircleOutlined style={{ color: '#ff4d4f', fontSize: '20px' }} />
                        )
                      }
                      title={
                        <Space>
                          <Text strong>{item.name}</Text>
                          <Tag color={item.available ? 'success' : 'error'}>
                            {item.available ? 'Available' : 'Missing'}
                          </Tag>
                        </Space>
                      }
                      description={
                        <div>
                          <div>{item.description}</div>
                          {item.version && (
                            <Text type="secondary">Version: {item.version}</Text>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </div>

            {missingDependencies.length > 0 && (
              <div>
                <Title level={4}>Installation Instructions</Title>
                {missingDependencies.map((dep) => (
                  <Card 
                    key={dep.name}
                    size="small" 
                    title={`Install ${dep.name}`}
                    style={{ marginBottom: '16px' }}
                  >
                    <List
                      size="small"
                      dataSource={dep.instructions}
                      renderItem={(instruction) => (
                        <List.Item>
                          <Text code>{instruction}</Text>
                        </List.Item>
                      )}
                    />
                  </Card>
                ))}
              </div>
            )}

            <Alert
              message="Installation Help"
              description={
                <div>
                  <Paragraph>
                    After installing the required dependencies, click the "Check Again" button below.
                  </Paragraph>
                  <Paragraph>
                    <Text strong>macOS Users:</Text> We recommend using Homebrew for easy installation.
                    If you don't have Homebrew, install it from{' '}
                    <Text code>https://brew.sh</Text>
                  </Paragraph>
                </div>
              }
              type="info"
              showIcon
            />

            <div style={{ textAlign: 'center' }}>
              <Button 
                type="primary" 
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                size="large"
              >
                Check Again
              </Button>
            </div>
          </Space>
        </Card>
      </Content>
    </Layout>
  );
};
