import React, { useState, useEffect } from 'react';
import { 
  Button, 
  Space, 
  Typography, 
  Card, 
  Select, 
  Alert,
  message,
  Descriptions
} from 'antd';
import { 
  FileTextOutlined, 
  CheckCircleOutlined,
  PlayCircleOutlined 
} from '@ant-design/icons';
import { 
  transcribeAudio, 
  getWhisperModels, 
  getSupportedLanguages,
  formatFileSize,
  formatDuration 
} from '../../services/tauri-api';
import { useApp } from '../../contexts/AppContext';
import type { LanguageOption } from '../../types';

const { Title, Text } = Typography;
const { Option } = Select;

interface SpeechRecognizerProps {
  onNext: () => void;
}

export const SpeechRecognizer: React.FC<SpeechRecognizerProps> = ({ onNext }) => {
  const { state, actions } = useApp();
  const [models, setModels] = useState<string[]>([]);
  const [languages, setLanguages] = useState<LanguageOption[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('base');
  const [selectedLanguage, setSelectedLanguage] = useState<string>('auto');
  const [isTranscribing, setIsTranscribing] = useState(false);

  useEffect(() => {
    const loadOptions = async () => {
      try {
        const [modelList, languageList] = await Promise.all([
          getWhisperModels(),
          getSupportedLanguages(),
        ]);
        
        setModels(modelList);
        setLanguages(languageList);
        
        // Set default language from settings
        if (state.settings.language) {
          setSelectedLanguage(state.settings.language);
        }
      } catch (error) {
        console.error('Failed to load options:', error);
        message.error('Failed to load speech recognition options');
      }
    };

    loadOptions();
  }, [state.settings.language]);

  const handleTranscribe = async () => {
    if (!state.currentAudio) {
      message.error('No audio file available');
      return;
    }

    try {
      setIsTranscribing(true);
      actions.updateProcessingStatus({
        stage: 'transcribing',
        progress: 0,
        message: 'Starting speech recognition...',
      });

      const subtitleData = await transcribeAudio(
        state.currentAudio.path,
        selectedLanguage,
        selectedModel
      );

      actions.setCurrentSubtitle(subtitleData);
      actions.updateProcessingStatus({
        stage: 'idle',
        progress: 100,
        message: 'Speech recognition completed',
      });

      message.success('Subtitles generated successfully');
    } catch (error) {
      console.error('Speech recognition failed:', error);
      message.error('Failed to generate subtitles');
      actions.updateProcessingStatus({
        stage: 'error',
        progress: 0,
        message: 'Speech recognition failed',
      });
    } finally {
      setIsTranscribing(false);
    }
  };

  const handleLanguageChange = (language: string) => {
    setSelectedLanguage(language);
    // Update settings
    actions.updateSettings({
      language: language,
    });
  };

  if (state.currentSubtitle) {
    return (
      <div>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <CheckCircleOutlined 
              style={{ fontSize: '48px', color: '#52c41a', marginBottom: '16px' }} 
            />
            <Title level={4}>Subtitles Generated Successfully</Title>
          </div>

          <Card>
            <Descriptions title="Subtitle Information" bordered column={1}>
              <Descriptions.Item label="Language">
                {state.currentSubtitle.language}
              </Descriptions.Item>
              <Descriptions.Item label="Total Entries">
                {state.currentSubtitle.entries.length}
              </Descriptions.Item>
              <Descriptions.Item label="Duration">
                {formatDuration(state.currentSubtitle.total_duration)}
              </Descriptions.Item>
              <Descriptions.Item label="Model Used">
                {selectedModel}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          <Card title="Sample Entries" size="small">
            {state.currentSubtitle.entries.slice(0, 3).map((entry, index) => (
              <div key={index} style={{ marginBottom: '12px', padding: '8px', background: '#f5f5f5', borderRadius: '4px' }}>
                <Text strong>
                  {formatDuration(entry.start_time)} → {formatDuration(entry.end_time)}
                </Text>
                <br />
                <Text>{entry.text}</Text>
              </div>
            ))}
            {state.currentSubtitle.entries.length > 3 && (
              <Text type="secondary">
                ... and {state.currentSubtitle.entries.length - 3} more entries
              </Text>
            )}
          </Card>

          <div style={{ textAlign: 'center' }}>
            <Space>
              <Button 
                icon={<FileTextOutlined />}
                onClick={() => actions.setCurrentSubtitle(null)}
              >
                Generate Again
              </Button>
              <Button type="primary" onClick={onNext}>
                Continue to AI Content Generation
              </Button>
            </Space>
          </div>
        </Space>
      </div>
    );
  }

  if (!state.currentAudio) {
    return (
      <Alert
        message="No Audio File Available"
        description="Please extract audio from a video file first before generating subtitles."
        type="warning"
        showIcon
      />
    );
  }

  return (
    <div>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <div style={{ textAlign: 'center' }}>
          <FileTextOutlined 
            style={{ fontSize: '48px', color: '#1890ff', marginBottom: '16px' }} 
          />
          <Title level={4}>Generate Subtitles</Title>
          <Text type="secondary">
            Use Whisper to convert speech to text and generate subtitles
          </Text>
        </div>

        <Card title="Speech Recognition Settings">
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <div>
              <Text strong>Language:</Text>
              <Select
                value={selectedLanguage}
                onChange={handleLanguageChange}
                style={{ width: '100%', marginTop: '8px' }}
                size="large"
              >
                {languages.map((lang) => (
                  <Option key={lang.code} value={lang.code}>
                    {lang.name}
                  </Option>
                ))}
              </Select>
            </div>

            <div>
              <Text strong>Whisper Model:</Text>
              <Select
                value={selectedModel}
                onChange={setSelectedModel}
                style={{ width: '100%', marginTop: '8px' }}
                size="large"
              >
                {models.map((model) => (
                  <Option key={model} value={model}>
                    <div>
                      <div>{model}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {model === 'tiny' && 'Fastest, least accurate'}
                        {model === 'base' && 'Good balance of speed and accuracy'}
                        {model === 'small' && 'Better accuracy, slower'}
                        {model === 'medium' && 'High accuracy, slower'}
                        {model === 'large' && 'Best accuracy, slowest'}
                      </div>
                    </div>
                  </Option>
                ))}
              </Select>
            </div>
          </Space>
        </Card>

        <Card title="Source Audio Information">
          <Descriptions column={1}>
            <Descriptions.Item label="File Name">
              {state.currentAudio.name}
            </Descriptions.Item>
            <Descriptions.Item label="File Size">
              {formatFileSize(state.currentAudio.size)}
            </Descriptions.Item>
            <Descriptions.Item label="Duration">
              {formatDuration(state.currentAudio.duration)}
            </Descriptions.Item>
            <Descriptions.Item label="Bitrate">
              {state.currentAudio.bitrate} kbps
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Alert
          message="Speech Recognition Process"
          description={
            <div>
              <p>This process will analyze the audio and generate subtitles with timestamps.</p>
              <p><strong>Processing time:</strong> Depends on audio length and selected model.</p>
              <p><strong>Accuracy:</strong> Higher quality models provide better accuracy but take longer.</p>
            </div>
          }
          type="info"
          showIcon
        />

        <div style={{ textAlign: 'center' }}>
          <Button 
            type="primary" 
            size="large"
            icon={<PlayCircleOutlined />}
            onClick={handleTranscribe}
            loading={isTranscribing}
          >
            {isTranscribing ? 'Generating Subtitles...' : 'Generate Subtitles'}
          </Button>
        </div>
      </Space>
    </div>
  );
};
