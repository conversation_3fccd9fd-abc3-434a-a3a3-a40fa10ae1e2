import React from 'react';
import { Progress, Space, Typography, Tag } from 'antd';
import { 
  LoadingOutlined, 
  CheckCircleOutlined, 
  ExclamationCircleOutlined 
} from '@ant-design/icons';
import { useApp } from '../../contexts/AppContext';

const { Text } = Typography;

export const ProcessingProgress: React.FC = () => {
  const { state } = useApp();
  const { processingStatus } = state;

  const getStatusColor = () => {
    switch (processingStatus.stage) {
      case 'complete':
        return 'success';
      case 'error':
        return 'exception';
      default:
        return 'active';
    }
  };

  const getStatusIcon = () => {
    switch (processingStatus.stage) {
      case 'complete':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getStageLabel = () => {
    switch (processingStatus.stage) {
      case 'idle':
        return 'Ready';
      case 'validating':
        return 'Validating';
      case 'extracting':
        return 'Extracting Audio';
      case 'transcribing':
        return 'Generating Subtitles';
      case 'generating':
        return 'Generating AI Content';
      case 'complete':
        return 'Complete';
      case 'error':
        return 'Error';
      default:
        return 'Processing';
    }
  };

  if (processingStatus.stage === 'idle') {
    return null;
  }

  return (
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Space>
          {getStatusIcon()}
          <Text strong>{getStageLabel()}</Text>
          <Tag color={getStatusColor()}>{processingStatus.stage.toUpperCase()}</Tag>
        </Space>
        
        {processingStatus.current_file && (
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {processingStatus.current_file}
          </Text>
        )}
      </div>

      <Progress
        percent={Math.round(processingStatus.progress)}
        status={getStatusColor()}
        strokeColor={{
          '0%': '#108ee9',
          '100%': '#87d068',
        }}
        showInfo={true}
        format={(percent) => `${percent}%`}
      />

      <Text type="secondary" style={{ fontSize: '14px' }}>
        {processingStatus.message}
      </Text>

      {processingStatus.stage === 'error' && (
        <div style={{ 
          padding: '8px 12px', 
          background: '#fff2f0', 
          border: '1px solid #ffccc7',
          borderRadius: '6px'
        }}>
          <Text type="danger" style={{ fontSize: '12px' }}>
            {processingStatus.message}
          </Text>
        </div>
      )}
    </Space>
  );
};
