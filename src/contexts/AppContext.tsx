import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { message } from 'antd';
import { loadUserSettings, saveUserSettings } from '../services/tauri-api';
import type {
  VideoFile,
  AudioFile,
  SubtitleData,
  AIContent,
  UserSettings,
  ProcessingStatus,
  ProcessingHistoryItem,
} from '../types';

// State interface
interface AppState {
  currentVideo: VideoFile | null;
  currentAudio: AudioFile | null;
  currentSubtitle: SubtitleData | null;
  currentAIContent: AIContent | null;
  processingStatus: ProcessingStatus;
  settings: UserSettings;
  history: ProcessingHistoryItem[];
  isLoading: boolean;
}

// Action types
type AppAction =
  | { type: 'SET_CURRENT_VIDEO'; payload: VideoFile | null }
  | { type: 'SET_CURRENT_AUDIO'; payload: AudioFile | null }
  | { type: 'SET_CURRENT_SUBTITLE'; payload: SubtitleData | null }
  | { type: 'SET_CURRENT_AI_CONTENT'; payload: AIContent | null }
  | { type: 'SET_PROCESSING_STATUS'; payload: ProcessingStatus }
  | { type: 'SET_SETTINGS'; payload: UserSettings }
  | { type: 'SET_HISTORY'; payload: ProcessingHistoryItem[] }
  | { type: 'ADD_HISTORY_ITEM'; payload: ProcessingHistoryItem }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'RESET_CURRENT_FILES' };

// Initial state
const initialState: AppState = {
  currentVideo: null,
  currentAudio: null,
  currentSubtitle: null,
  currentAIContent: null,
  processingStatus: {
    stage: 'idle',
    progress: 0,
    message: 'Ready',
  },
  settings: {
    ai_config: {
      base_url: 'https://api.openai.com/v1',
      api_key: '',
      model_name: 'gpt-4o',
      temperature: 0.7,
      max_tokens: 2048,
    },
    audio_quality: '192',
    language: 'auto',
    theme: 'light',
    output_directory: '',
  },
  history: [],
  isLoading: false,
};

// Reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_CURRENT_VIDEO':
      return { ...state, currentVideo: action.payload };
    case 'SET_CURRENT_AUDIO':
      return { ...state, currentAudio: action.payload };
    case 'SET_CURRENT_SUBTITLE':
      return { ...state, currentSubtitle: action.payload };
    case 'SET_CURRENT_AI_CONTENT':
      return { ...state, currentAIContent: action.payload };
    case 'SET_PROCESSING_STATUS':
      return { ...state, processingStatus: action.payload };
    case 'SET_SETTINGS':
      return { ...state, settings: action.payload };
    case 'SET_HISTORY':
      return { ...state, history: action.payload };
    case 'ADD_HISTORY_ITEM':
      return { ...state, history: [action.payload, ...state.history] };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'RESET_CURRENT_FILES':
      return {
        ...state,
        currentVideo: null,
        currentAudio: null,
        currentSubtitle: null,
        currentAIContent: null,
        processingStatus: {
          stage: 'idle',
          progress: 0,
          message: 'Ready',
        },
      };
    default:
      return state;
  }
};

// Context
interface AppContextType {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  actions: {
    setCurrentVideo: (video: VideoFile | null) => void;
    setCurrentAudio: (audio: AudioFile | null) => void;
    setCurrentSubtitle: (subtitle: SubtitleData | null) => void;
    setCurrentAIContent: (content: AIContent | null) => void;
    updateProcessingStatus: (status: Partial<ProcessingStatus>) => void;
    updateSettings: (settings: Partial<UserSettings>) => Promise<void>;
    addHistoryItem: (item: ProcessingHistoryItem) => void;
    resetCurrentFiles: () => void;
  };
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider component
export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load settings on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        const settings = await loadUserSettings();
        dispatch({ type: 'SET_SETTINGS', payload: settings });
      } catch (error) {
        console.error('Failed to load settings:', error);
        message.error('Failed to load settings');
      } finally {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    loadSettings();
  }, []);

  // Actions
  const actions = {
    setCurrentVideo: (video: VideoFile | null) => {
      dispatch({ type: 'SET_CURRENT_VIDEO', payload: video });
    },

    setCurrentAudio: (audio: AudioFile | null) => {
      dispatch({ type: 'SET_CURRENT_AUDIO', payload: audio });
    },

    setCurrentSubtitle: (subtitle: SubtitleData | null) => {
      dispatch({ type: 'SET_CURRENT_SUBTITLE', payload: subtitle });
    },

    setCurrentAIContent: (content: AIContent | null) => {
      dispatch({ type: 'SET_CURRENT_AI_CONTENT', payload: content });
    },

    updateProcessingStatus: (status: Partial<ProcessingStatus>) => {
      dispatch({
        type: 'SET_PROCESSING_STATUS',
        payload: { ...state.processingStatus, ...status },
      });
    },

    updateSettings: async (newSettings: Partial<UserSettings>) => {
      try {
        const updatedSettings = { ...state.settings, ...newSettings };
        await saveUserSettings(updatedSettings);
        dispatch({ type: 'SET_SETTINGS', payload: updatedSettings });
        message.success('Settings saved successfully');
      } catch (error) {
        console.error('Failed to save settings:', error);
        message.error('Failed to save settings');
        throw error;
      }
    },

    addHistoryItem: (item: ProcessingHistoryItem) => {
      dispatch({ type: 'ADD_HISTORY_ITEM', payload: item });
    },

    resetCurrentFiles: () => {
      dispatch({ type: 'RESET_CURRENT_FILES' });
    },
  };

  return (
    <AppContext.Provider value={{ state, dispatch, actions }}>
      {children}
    </AppContext.Provider>
  );
};

// Hook to use the context
export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
