# Speech Extract - 需求文档

## 项目概述

Speech Extract 是一个基于 Tauri 开发的 macOS 桌面应用，用于从视频文件中提取音频、生成字幕，并使用 AI 生成相关的 SEO 内容和 YouTube 优化建议。

## 功能需求

### 1. 视频文件处理
- **文件选择**: 用户可以通过文件选择器打开视频文件
- **支持格式**: 支持常见视频格式（MP4, MOV, AVI, MKV 等）
- **文件验证**: 验证选择的文件是否为有效的视频文件

### 2. 音频提取
- **FFmpeg 集成**: 使用本地 FFmpeg 从视频中提取 MP3 音频
- **音频质量**: 支持不同的音频质量设置（128kbps, 192kbps, 320kbps）
- **进度显示**: 显示音频提取的实时进度
- **错误处理**: 处理 FFmpeg 执行过程中的错误

### 3. 语音识别与字幕生成
- **Whisper 集成**: 使用本地 Whisper 模型进行语音识别
- **SRT 生成**: 生成标准 SRT 字幕文件格式
- **语言支持**: 支持多种语言的语音识别
- **时间戳精度**: 确保字幕时间戳的准确性

### 4. AI 内容生成
- **API 配置**: 用户可以配置 AI API 的 baseUrl、key 和 model name
- **内容生成类型**:
  - SEO 推荐内容
  - YouTube 填写指南（标题、描述、标签）
  - 基于摘要的封面图生成建议
- **模型支持**: 支持 GPT-4o 等主流 AI 模型

### 5. 配置管理
- **API 设置**: 安全存储用户的 AI API 配置信息
- **偏好设置**: 保存用户的处理偏好（音频质量、语言等）
- **历史记录**: 记录处理过的文件历史

### 6. 文件管理
- **导出功能**: 支持导出生成的字幕文件和 AI 内容
- **文件组织**: 按项目或日期组织生成的文件
- **预览功能**: 在应用内预览生成的内容

## 非功能需求

### 1. 性能要求
- 支持大文件处理（最大 2GB 视频文件）
- 音频提取和语音识别的并行处理
- 响应式用户界面，不阻塞主线程

### 2. 安全要求
- API 密钥的安全存储（使用系统密钥链）
- 本地文件处理，保护用户隐私
- 安全的临时文件管理

### 3. 兼容性要求
- macOS 10.15 及以上版本
- 支持 Intel 和 Apple Silicon 芯片
- 自动检测和安装必要的依赖（FFmpeg, Whisper）

### 4. 用户体验要求
- 直观的拖拽式文件上传
- 清晰的进度指示和状态反馈
- 友好的错误提示和解决建议
- 支持暗色和亮色主题

## 技术约束

### 1. 开发框架
- 前端：React + TypeScript + Vite
- 后端：Rust + Tauri
- UI 组件库：待选择（建议 Ant Design 或 Material-UI）

### 2. 外部依赖
- FFmpeg：音频提取
- Whisper：语音识别
- AI API：内容生成（OpenAI GPT-4o 等）

### 3. 存储要求
- 本地配置存储
- 临时文件管理
- 处理历史记录

## 用户故事

### 故事 1：视频处理新手用户
作为一个视频处理新手，我希望能够简单地拖拽视频文件到应用中，自动完成音频提取和字幕生成，这样我就能快速获得视频的文字内容。

### 故事 2：YouTube 内容创作者
作为一个 YouTube 内容创作者，我希望能够上传我的视频，自动生成 SEO 优化的标题、描述和标签建议，以及封面图创意，这样我就能提高视频的曝光率。

### 故事 3：多语言内容处理者
作为一个需要处理多语言视频的用户，我希望能够选择不同的语言模型进行语音识别，并获得准确的字幕，这样我就能处理各种语言的视频内容。

## 验收标准

### 1. 基本功能验收
- [ ] 能够成功选择和加载视频文件
- [ ] 能够使用 FFmpeg 提取音频文件
- [ ] 能够使用 Whisper 生成准确的字幕
- [ ] 能够配置和使用 AI API 生成内容

### 2. 性能验收
- [ ] 处理 1GB 视频文件的时间不超过视频时长的 2 倍
- [ ] 界面响应时间不超过 100ms
- [ ] 内存使用不超过 2GB

### 3. 用户体验验收
- [ ] 新用户能在 5 分钟内完成首次视频处理
- [ ] 错误信息清晰易懂，提供解决方案
- [ ] 支持键盘快捷键操作

## 项目里程碑

### 阶段 1：基础框架搭建（1-2 周）
- 项目架构设计
- UI 框架选择和基础组件开发
- Tauri 后端基础功能实现

### 阶段 2：核心功能开发（3-4 周）
- 文件选择和验证
- FFmpeg 集成和音频提取
- Whisper 集成和字幕生成

### 阶段 3：AI 功能集成（2-3 周）
- AI API 配置管理
- 内容生成功能实现
- 结果展示和导出

### 阶段 4：优化和测试（1-2 周）
- 性能优化
- 错误处理完善
- 用户测试和反馈收集

### 阶段 5：发布准备（1 周）
- 应用打包和签名
- 文档完善
- 发布准备
