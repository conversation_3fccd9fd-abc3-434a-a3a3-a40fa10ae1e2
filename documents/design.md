# Speech Extract - 设计文档

## 系统架构

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (React + TS)                    │
├─────────────────────────────────────────────────────────────┤
│                    Tauri Bridge Layer                       │
├─────────────────────────────────────────────────────────────┤
│                    Backend (Rust)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐│
│  │File Handler │ │Audio Extract│ │Speech Recog │ │AI Client││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘│
├─────────────────────────────────────────────────────────────┤
│                External Dependencies                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │   FFmpeg    │ │   Whisper   │ │      AI APIs            ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

## 前端设计

### 技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI 库**: Ant Design (推荐) 或 Material-UI
- **状态管理**: React Context + useReducer
- **样式**: CSS Modules 或 Styled Components

### 页面结构
```
App
├── Layout
│   ├── Header (应用标题、设置按钮)
│   ├── Sidebar (导航菜单)
│   └── Content (主要内容区域)
├── Pages
│   ├── Home (文件上传和处理)
│   ├── Settings (API 配置)
│   ├── History (处理历史)
│   └── Results (结果展示)
└── Components
    ├── FileUploader
    ├── ProgressBar
    ├── VideoPlayer
    ├── SubtitleEditor
    └── AIContentDisplay
```

### 状态管理
```typescript
interface AppState {
  currentFile: VideoFile | null;
  processingStatus: ProcessingStatus;
  settings: UserSettings;
  history: ProcessingHistory[];
  results: ProcessingResults | null;
}

interface ProcessingStatus {
  stage: 'idle' | 'extracting' | 'transcribing' | 'generating' | 'complete';
  progress: number;
  message: string;
}

interface UserSettings {
  aiConfig: {
    baseUrl: string;
    apiKey: string;
    modelName: string;
  };
  audioQuality: '128' | '192' | '320';
  language: string;
  theme: 'light' | 'dark';
}
```

## 后端设计

### Rust 模块结构
```
src/
├── main.rs              # 应用入口
├── lib.rs               # 库入口和 Tauri 配置
├── commands/            # Tauri 命令
│   ├── mod.rs
│   ├── file_handler.rs  # 文件处理命令
│   ├── audio_extract.rs # 音频提取命令
│   ├── speech_recog.rs  # 语音识别命令
│   └── ai_client.rs     # AI 客户端命令
├── services/            # 业务逻辑服务
│   ├── mod.rs
│   ├── ffmpeg.rs        # FFmpeg 服务
│   ├── whisper.rs       # Whisper 服务
│   └── ai_service.rs    # AI 服务
├── models/              # 数据模型
│   ├── mod.rs
│   ├── video.rs         # 视频文件模型
│   ├── audio.rs         # 音频文件模型
│   ├── subtitle.rs      # 字幕模型
│   └── ai_content.rs    # AI 内容模型
├── utils/               # 工具函数
│   ├── mod.rs
│   ├── file_utils.rs    # 文件工具
│   ├── config.rs        # 配置管理
│   └── error.rs         # 错误处理
└── tests/               # 测试文件
```

### 核心 Tauri 命令

```rust
// 文件处理命令
#[tauri::command]
async fn select_video_file() -> Result<VideoFile, String>;

#[tauri::command]
async fn validate_video_file(path: String) -> Result<bool, String>;

// 音频提取命令
#[tauri::command]
async fn extract_audio(
    video_path: String,
    output_path: String,
    quality: String,
) -> Result<String, String>;

// 语音识别命令
#[tauri::command]
async fn transcribe_audio(
    audio_path: String,
    language: String,
) -> Result<SubtitleData, String>;

// AI 内容生成命令
#[tauri::command]
async fn generate_ai_content(
    subtitle_text: String,
    content_type: String,
    api_config: AIConfig,
) -> Result<AIContent, String>;

// 配置管理命令
#[tauri::command]
async fn save_settings(settings: UserSettings) -> Result<(), String>;

#[tauri::command]
async fn load_settings() -> Result<UserSettings, String>;
```

## 数据模型

### 视频文件模型
```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct VideoFile {
    pub path: String,
    pub name: String,
    pub size: u64,
    pub duration: f64,
    pub format: String,
    pub created_at: DateTime<Utc>,
}
```

### 字幕数据模型
```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct SubtitleData {
    pub entries: Vec<SubtitleEntry>,
    pub language: String,
    pub total_duration: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SubtitleEntry {
    pub index: u32,
    pub start_time: f64,
    pub end_time: f64,
    pub text: String,
}
```

### AI 内容模型
```rust
#[derive(Debug, Serialize, Deserialize)]
pub struct AIContent {
    pub seo_content: SEOContent,
    pub youtube_guide: YouTubeGuide,
    pub cover_suggestions: Vec<CoverSuggestion>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SEOContent {
    pub keywords: Vec<String>,
    pub meta_description: String,
    pub title_suggestions: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct YouTubeGuide {
    pub title: String,
    pub description: String,
    pub tags: Vec<String>,
    pub thumbnail_tips: Vec<String>,
}
```

## 外部依赖集成

### FFmpeg 集成
```rust
pub struct FFmpegService {
    ffmpeg_path: String,
}

impl FFmpegService {
    pub async fn extract_audio(
        &self,
        input_path: &str,
        output_path: &str,
        quality: &str,
    ) -> Result<(), FFmpegError> {
        let mut cmd = Command::new(&self.ffmpeg_path);
        cmd.args([
            "-i", input_path,
            "-vn",
            "-acodec", "mp3",
            "-ab", &format!("{}k", quality),
            output_path,
        ]);
        
        // 执行命令并处理进度
        // ...
    }
}
```

### Whisper 集成
```rust
pub struct WhisperService {
    model_path: String,
}

impl WhisperService {
    pub async fn transcribe(
        &self,
        audio_path: &str,
        language: &str,
    ) -> Result<SubtitleData, WhisperError> {
        // 调用 Whisper 进行语音识别
        // 可以使用 whisper-rs 或直接调用 Python whisper
        // ...
    }
}
```

### AI 服务集成
```rust
pub struct AIService {
    client: reqwest::Client,
}

impl AIService {
    pub async fn generate_content(
        &self,
        subtitle_text: &str,
        content_type: &str,
        config: &AIConfig,
    ) -> Result<AIContent, AIError> {
        let prompt = self.build_prompt(subtitle_text, content_type);
        
        let response = self.client
            .post(&format!("{}/chat/completions", config.base_url))
            .header("Authorization", &format!("Bearer {}", config.api_key))
            .json(&json!({
                "model": config.model_name,
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.7
            }))
            .send()
            .await?;
            
        // 解析响应并返回结构化内容
        // ...
    }
}
```

## 配置管理

### 安全存储
```rust
use keyring::Entry;

pub struct ConfigManager {
    app_name: String,
}

impl ConfigManager {
    pub fn save_api_key(&self, service: &str, api_key: &str) -> Result<(), ConfigError> {
        let entry = Entry::new(&self.app_name, service)?;
        entry.set_password(api_key)?;
        Ok(())
    }
    
    pub fn load_api_key(&self, service: &str) -> Result<String, ConfigError> {
        let entry = Entry::new(&self.app_name, service)?;
        Ok(entry.get_password()?)
    }
}
```

## 错误处理

### 统一错误类型
```rust
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("File operation failed: {0}")]
    FileError(#[from] std::io::Error),
    
    #[error("FFmpeg error: {0}")]
    FFmpegError(String),
    
    #[error("Whisper error: {0}")]
    WhisperError(String),
    
    #[error("AI service error: {0}")]
    AIError(String),
    
    #[error("Configuration error: {0}")]
    ConfigError(String),
}
```

## 性能优化

### 异步处理
- 使用 Tokio 异步运行时
- 音频提取和语音识别并行处理
- 进度回调和状态更新

### 内存管理
- 流式处理大文件
- 及时清理临时文件
- 合理的缓存策略

### 用户体验优化
- 非阻塞 UI 操作
- 实时进度反馈
- 错误恢复机制

## 测试策略

### 单元测试
- 每个服务模块的独立测试
- 数据模型的序列化/反序列化测试
- 工具函数的边界条件测试

### 集成测试
- FFmpeg 集成测试
- Whisper 集成测试
- AI API 集成测试

### 端到端测试
- 完整的视频处理流程测试
- 用户界面交互测试
- 错误场景测试

## 部署和分发

### 构建配置
- 支持 Intel 和 Apple Silicon
- 代码签名和公证
- 自动更新机制

### 依赖管理
- FFmpeg 二进制文件打包
- Whisper 模型文件管理
- 运行时依赖检查
