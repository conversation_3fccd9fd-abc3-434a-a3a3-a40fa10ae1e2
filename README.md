# Speech Extract

一个基于 Tauri 的 macOS 桌面应用，用于视频处理工作流：从视频提取音频、生成字幕，并使用 AI 生成 SEO 内容和 YouTube 优化建议。

## 功能特性

- 🎥 **视频处理**: 支持多种视频格式 (MP4, MOV, AVI, MKV 等)
- 🎵 **音频提取**: 使用 FFmpeg 提取高质量音频
- 📝 **字幕生成**: 使用 Whisper 进行语音识别
- 🤖 **AI 内容生成**: 生成 SEO 内容和 YouTube 优化建议
- ⚙️ **配置管理**: 安全存储 AI API 配置
- 📊 **处理历史**: 记录和管理处理历史

## 系统要求

### 运行环境
- macOS 10.15 或更高版本
- Intel 或 Apple Silicon 芯片

### 必需依赖
- **FFmpeg**: 用于视频处理和音频提取
- **Whisper**: 用于语音识别和字幕生成

### 开发环境
- Node.js 16 或更高版本
- Rust 1.70 或更高版本
- Tauri CLI

## 安装依赖

### 1. 安装 FFmpeg
```bash
# 使用 Homebrew (推荐)
brew install ffmpeg

# 或从官网下载
# https://ffmpeg.org/download.html
```

### 2. 安装 Whisper
```bash
# 使用 pip
pip install openai-whisper

# 或使用 conda
conda install -c conda-forge openai-whisper
```

### 3. 验证安装
```bash
# 检查 FFmpeg
ffmpeg -version

# 检查 Whisper
whisper --help
```
