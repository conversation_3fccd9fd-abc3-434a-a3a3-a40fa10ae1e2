use reqwest::Client;
use serde_json::{json, Value};
use crate::models::{AIConfig, AIContent, SEOContent, YouTubeGuide, CoverSuggestion, ContentType};
use anyhow::{Result, anyhow};
use tracing::{info, error, debug};

pub struct AIService {
    client: Client,
}

impl AIService {
    pub fn new() -> Self {
        Self {
            client: Client::new(),
        }
    }

    pub async fn generate_content(
        &self,
        subtitle_text: &str,
        content_type: ContentType,
        config: &AIConfig,
    ) -> Result<AIContent> {
        info!("Generating AI content for type: {:?}", content_type);

        let mut ai_content = AIContent::new();

        match content_type {
            ContentType::SEO => {
                ai_content.seo_content = self.generate_seo_content(subtitle_text, config).await?;
            }
            ContentType::YouTube => {
                ai_content.youtube_guide = self.generate_youtube_guide(subtitle_text, config).await?;
            }
            ContentType::Cover => {
                ai_content.cover_suggestions = self.generate_cover_suggestions(subtitle_text, config).await?;
            }
            ContentType::All => {
                ai_content.seo_content = self.generate_seo_content(subtitle_text, config).await?;
                ai_content.youtube_guide = self.generate_youtube_guide(subtitle_text, config).await?;
                ai_content.cover_suggestions = self.generate_cover_suggestions(subtitle_text, config).await?;
            }
        }

        info!("AI content generation completed successfully");
        Ok(ai_content)
    }

    async fn generate_seo_content(&self, subtitle_text: &str, config: &AIConfig) -> Result<SEOContent> {
        let prompt = format!(
            r#"Based on the following video transcript, generate SEO-optimized content:

Transcript:
{}

Please provide:
1. 10-15 relevant keywords
2. A compelling meta description (150-160 characters)
3. 5 SEO-friendly title suggestions
4. A brief content summary (2-3 sentences)

Format your response as JSON with the following structure:
{{
    "keywords": ["keyword1", "keyword2", ...],
    "meta_description": "description here",
    "title_suggestions": ["title1", "title2", ...],
    "content_summary": "summary here"
}}"#,
            subtitle_text
        );

        let response = self.call_ai_api(&prompt, config).await?;
        let seo_data: SEOContent = serde_json::from_value(response)?;
        Ok(seo_data)
    }

    async fn generate_youtube_guide(&self, subtitle_text: &str, config: &AIConfig) -> Result<YouTubeGuide> {
        let prompt = format!(
            r#"Based on the following video transcript, create a comprehensive YouTube optimization guide:

Transcript:
{}

Please provide:
1. An engaging YouTube title (60 characters or less)
2. A detailed description (2-3 paragraphs)
3. 10-15 relevant tags
4. 5 thumbnail creation tips
5. Best posting time recommendation
6. Target audience description

Format your response as JSON with the following structure:
{{
    "title": "title here",
    "description": "description here",
    "tags": ["tag1", "tag2", ...],
    "thumbnail_tips": ["tip1", "tip2", ...],
    "best_posting_time": "time recommendation",
    "target_audience": "audience description"
}}"#,
            subtitle_text
        );

        let response = self.call_ai_api(&prompt, config).await?;
        let youtube_data: YouTubeGuide = serde_json::from_value(response)?;
        Ok(youtube_data)
    }

    async fn generate_cover_suggestions(&self, subtitle_text: &str, config: &AIConfig) -> Result<Vec<CoverSuggestion>> {
        let prompt = format!(
            r#"Based on the following video transcript, suggest 3 different cover/thumbnail designs:

Transcript:
{}

For each design, provide:
1. A descriptive title
2. Design description
3. Visual style (e.g., minimalist, bold, professional)
4. Color scheme (3-4 colors)
5. Key visual elements to include

Format your response as JSON array with the following structure:
[
    {{
        "title": "design title",
        "description": "design description",
        "style": "visual style",
        "color_scheme": ["color1", "color2", "color3"],
        "elements": ["element1", "element2", ...]
    }},
    ...
]"#,
            subtitle_text
        );

        let response = self.call_ai_api(&prompt, config).await?;
        let cover_suggestions: Vec<CoverSuggestion> = serde_json::from_value(response)?;
        Ok(cover_suggestions)
    }

    async fn call_ai_api(&self, prompt: &str, config: &AIConfig) -> Result<Value> {
        debug!("Calling AI API with model: {}", config.model_name);

        let request_body = json!({
            "model": config.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": config.temperature,
            "max_tokens": config.max_tokens
        });

        let response = self.client
            .post(&format!("{}/chat/completions", config.base_url))
            .header("Authorization", &format!("Bearer {}", config.api_key))
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("AI API error: {}", error_text);
            return Err(anyhow!("AI API request failed: {}", error_text));
        }

        let response_json: Value = response.json().await?;
        
        // Extract content from OpenAI response format
        if let Some(choices) = response_json["choices"].as_array() {
            if let Some(first_choice) = choices.first() {
                if let Some(content) = first_choice["message"]["content"].as_str() {
                    // Try to parse the content as JSON
                    match serde_json::from_str::<Value>(content) {
                        Ok(parsed_content) => return Ok(parsed_content),
                        Err(_) => {
                            // If parsing fails, try to extract JSON from the content
                            if let Some(json_start) = content.find('{') {
                                if let Some(json_end) = content.rfind('}') {
                                    let json_str = &content[json_start..=json_end];
                                    match serde_json::from_str::<Value>(json_str) {
                                        Ok(parsed_content) => return Ok(parsed_content),
                                        Err(e) => {
                                            error!("Failed to parse AI response as JSON: {}", e);
                                            return Err(anyhow!("Invalid JSON response from AI"));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Err(anyhow!("Invalid response format from AI API"))
    }

    pub async fn test_connection(&self, config: &AIConfig) -> Result<bool> {
        let test_prompt = "Hello, this is a test message. Please respond with 'OK'.";
        
        let request_body = json!({
            "model": config.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": test_prompt
                }
            ],
            "max_tokens": 10
        });

        let response = self.client
            .post(&format!("{}/chat/completions", config.base_url))
            .header("Authorization", &format!("Bearer {}", config.api_key))
            .header("Content-Type", "application/json")
            .json(&request_body)
            .send()
            .await?;

        Ok(response.status().is_success())
    }
}
