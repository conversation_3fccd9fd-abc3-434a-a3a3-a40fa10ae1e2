use std::process::Stdio;
use std::path::Path;
use tokio::process::Command as TokioCommand;
use crate::models::{SubtitleData, SubtitleEntry};
use anyhow::{Result, anyhow};
use tracing::{info, error, debug};

pub struct WhisperService {
    whisper_path: String,
}

impl WhisperService {
    pub fn new() -> Result<Self> {
        let whisper_path = Self::find_whisper()?;
        Ok(Self { whisper_path })
    }

    fn find_whisper() -> Result<String> {
        // Try to find whisper in PATH
        if let Ok(output) = std::process::Command::new("which").arg("whisper").output() {
            if output.status.success() {
                let path = String::from_utf8(output.stdout)?;
                return Ok(path.trim().to_string());
            }
        }

        // Try common installation paths
        let common_paths = [
            "/usr/local/bin/whisper",
            "/opt/homebrew/bin/whisper",
            "/usr/bin/whisper",
            "whisper", // Try direct command
        ];

        for path in &common_paths {
            if path == &"whisper" {
                // Test if whisper command works
                if let Ok(output) = std::process::Command::new(path).arg("--help").output() {
                    if output.status.success() {
                        return Ok(path.to_string());
                    }
                }
            } else if Path::new(path).exists() {
                return Ok(path.to_string());
            }
        }

        Err(anyhow!("Whisper not found. Please install OpenAI Whisper first."))
    }

    pub async fn transcribe_audio(
        &self,
        audio_path: &str,
        language: &str,
        model: &str,
    ) -> Result<SubtitleData> {
        info!("Starting transcription of {} with language: {}", audio_path, language);

        let output_dir = Path::new(audio_path).parent().unwrap();
        let mut cmd = TokioCommand::new(&self.whisper_path);
        
        cmd.args([
            audio_path,
            "--model", model,
            "--output_format", "srt",
            "--output_dir", output_dir.to_str().unwrap(),
        ]);

        // Add language parameter if not auto-detect
        if language != "auto" {
            cmd.args(["--language", language]);
        }

        cmd.stdout(Stdio::piped());
        cmd.stderr(Stdio::piped());

        debug!("Executing Whisper command: {:?}", cmd);

        let output = cmd.output().await?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            error!("Whisper failed: {}", stderr);
            return Err(anyhow!("Whisper transcription failed: {}", stderr));
        }

        // Find the generated SRT file
        let audio_stem = Path::new(audio_path).file_stem().unwrap();
        let srt_path = output_dir.join(format!("{}.srt", audio_stem.to_string_lossy()));

        if !srt_path.exists() {
            return Err(anyhow!("SRT file not generated"));
        }

        // Parse the SRT file
        let srt_content = tokio::fs::read_to_string(&srt_path).await?;
        let subtitle_data = self.parse_srt_content(&srt_content, language)?;

        info!("Transcription completed successfully with {} entries", subtitle_data.entries.len());
        Ok(subtitle_data)
    }

    fn parse_srt_content(&self, content: &str, language: &str) -> Result<SubtitleData> {
        let mut subtitle_data = SubtitleData::new(language.to_string());
        let mut entries = Vec::new();
        
        let blocks: Vec<&str> = content.split("\n\n").collect();
        
        for block in blocks {
            if block.trim().is_empty() {
                continue;
            }
            
            let lines: Vec<&str> = block.lines().collect();
            if lines.len() < 3 {
                continue;
            }
            
            // Parse index
            let index: u32 = lines[0].trim().parse().unwrap_or(0);
            
            // Parse time range
            let time_line = lines[1];
            let (start_time, end_time) = self.parse_time_range(time_line)?;
            
            // Parse text (can be multiple lines)
            let text = lines[2..].join("\n");
            
            let entry = SubtitleEntry::new(index, start_time, end_time, text);
            entries.push(entry);
        }
        
        subtitle_data.entries = entries;
        
        // Calculate total duration
        if let Some(last_entry) = subtitle_data.entries.last() {
            subtitle_data.total_duration = last_entry.end_time;
        }
        
        Ok(subtitle_data)
    }

    fn parse_time_range(&self, time_line: &str) -> Result<(f64, f64)> {
        let parts: Vec<&str> = time_line.split(" --> ").collect();
        if parts.len() != 2 {
            return Err(anyhow!("Invalid time range format"));
        }
        
        let start_time = self.parse_srt_time(parts[0].trim())?;
        let end_time = self.parse_srt_time(parts[1].trim())?;
        
        Ok((start_time, end_time))
    }

    fn parse_srt_time(&self, time_str: &str) -> Result<f64> {
        // Format: HH:MM:SS,mmm
        let parts: Vec<&str> = time_str.split(',').collect();
        if parts.len() != 2 {
            return Err(anyhow!("Invalid SRT time format"));
        }
        
        let time_part = parts[0];
        let millis_part: f64 = parts[1].parse::<f64>()? / 1000.0;
        
        let time_components: Vec<&str> = time_part.split(':').collect();
        if time_components.len() != 3 {
            return Err(anyhow!("Invalid time components"));
        }
        
        let hours: f64 = time_components[0].parse()?;
        let minutes: f64 = time_components[1].parse()?;
        let seconds: f64 = time_components[2].parse()?;
        
        Ok(hours * 3600.0 + minutes * 60.0 + seconds + millis_part)
    }

    pub fn is_available(&self) -> bool {
        std::process::Command::new(&self.whisper_path)
            .arg("--help")
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false)
    }

    pub fn get_available_models() -> Vec<&'static str> {
        vec![
            "tiny",
            "base", 
            "small",
            "medium",
            "large",
        ]
    }

    pub fn get_supported_languages() -> Vec<(&'static str, &'static str)> {
        vec![
            ("auto", "Auto Detect"),
            ("en", "English"),
            ("zh", "Chinese"),
            ("ja", "Japanese"),
            ("ko", "Korean"),
            ("es", "Spanish"),
            ("fr", "French"),
            ("de", "German"),
            ("it", "Italian"),
            ("pt", "Portuguese"),
            ("ru", "Russian"),
            ("ar", "Arabic"),
        ]
    }
}
