use keyring::Entry;
use crate::models::UserSettings;
use anyhow::{Result, anyhow};
use tracing::{info, error, debug};
use std::path::PathBuf;

pub struct ConfigService {
    app_name: String,
    config_dir: PathBuf,
}

impl ConfigService {
    pub fn new() -> Result<Self> {
        let app_name = "speech-extract".to_string();

        // Get user config directory
        let config_dir = dirs::config_dir()
            .ok_or_else(|| anyhow!("Could not find config directory"))?
            .join(&app_name);

        // Create config directory if it doesn't exist
        if !config_dir.exists() {
            std::fs::create_dir_all(&config_dir)?;
        }

        Ok(Self {
            app_name,
            config_dir,
        })
    }

    pub async fn save_settings(&self, settings: &UserSettings) -> Result<()> {
        info!("Saving user settings");

        // Save API key securely in keychain
        if !settings.ai_config.api_key.is_empty() {
            self.save_api_key(&settings.ai_config.api_key).await?;
        }

        // Save other settings to file (without API key)
        let mut settings_to_save = settings.clone();
        settings_to_save.ai_config.api_key = "".to_string(); // Don't save API key in file

        let config_file = self.config_dir.join("settings.json");
        let settings_json = serde_json::to_string_pretty(&settings_to_save)?;

        tokio::fs::write(&config_file, settings_json).await?;

        info!("Settings saved successfully");
        Ok(())
    }

    pub async fn load_settings(&self) -> Result<UserSettings> {
        debug!("Loading user settings");

        let config_file = self.config_dir.join("settings.json");

        let mut settings = if config_file.exists() {
            let settings_content = tokio::fs::read_to_string(&config_file).await?;
            serde_json::from_str::<UserSettings>(&settings_content)?
        } else {
            UserSettings::default()
        };

        // Load API key from keychain
        if let Ok(api_key) = self.load_api_key().await {
            settings.ai_config.api_key = api_key;
        }

        debug!("Settings loaded successfully");
        Ok(settings)
    }

    async fn save_api_key(&self, api_key: &str) -> Result<()> {
        let entry = Entry::new(&self.app_name, "ai_api_key")?;
        entry.set_password(api_key)?;
        Ok(())
    }

    async fn load_api_key(&self) -> Result<String> {
        let entry = Entry::new(&self.app_name, "ai_api_key")?;
        Ok(entry.get_password()?)
    }

    pub async fn delete_api_key(&self) -> Result<()> {
        let entry = Entry::new(&self.app_name, "ai_api_key")?;
        entry.delete_credential()?;
        Ok(())
    }

    pub async fn save_processing_history(&self, history_item: &ProcessingHistoryItem) -> Result<()> {
        let history_file = self.config_dir.join("history.json");

        let mut history = if history_file.exists() {
            let history_content = tokio::fs::read_to_string(&history_file).await?;
            serde_json::from_str::<Vec<ProcessingHistoryItem>>(&history_content)
                .unwrap_or_else(|_| Vec::new())
        } else {
            Vec::new()
        };

        history.push(history_item.clone());

        // Keep only last 100 items
        if history.len() > 100 {
            history.drain(0..history.len() - 100);
        }

        let history_json = serde_json::to_string_pretty(&history)?;
        tokio::fs::write(&history_file, history_json).await?;

        Ok(())
    }

    pub async fn load_processing_history(&self) -> Result<Vec<ProcessingHistoryItem>> {
        let history_file = self.config_dir.join("history.json");

        if history_file.exists() {
            let history_content = tokio::fs::read_to_string(&history_file).await?;
            let history = serde_json::from_str::<Vec<ProcessingHistoryItem>>(&history_content)
                .unwrap_or_else(|_| Vec::new());
            Ok(history)
        } else {
            Ok(Vec::new())
        }
    }

    pub fn get_temp_dir(&self) -> PathBuf {
        self.config_dir.join("temp")
    }

    pub fn get_output_dir(&self) -> PathBuf {
        self.config_dir.join("output")
    }

    pub async fn ensure_directories(&self) -> Result<()> {
        let temp_dir = self.get_temp_dir();
        let output_dir = self.get_output_dir();

        if !temp_dir.exists() {
            tokio::fs::create_dir_all(&temp_dir).await?;
        }

        if !output_dir.exists() {
            tokio::fs::create_dir_all(&output_dir).await?;
        }

        Ok(())
    }

    pub async fn cleanup_temp_files(&self) -> Result<()> {
        let temp_dir = self.get_temp_dir();

        if temp_dir.exists() {
            let mut entries = tokio::fs::read_dir(&temp_dir).await?;

            while let Some(entry) = entries.next_entry().await? {
                let path = entry.path();
                if path.is_file() {
                    if let Err(e) = tokio::fs::remove_file(&path).await {
                        error!("Failed to remove temp file {:?}: {}", path, e);
                    }
                }
            }
        }

        Ok(())
    }
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ProcessingHistoryItem {
    pub id: String,
    pub video_file_name: String,
    pub video_file_path: String,
    pub processing_time: f64,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub status: String,
    pub output_files: Vec<String>,
}

impl ProcessingHistoryItem {
    pub fn new(video_file_name: String, video_file_path: String) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            video_file_name,
            video_file_path,
            processing_time: 0.0,
            created_at: chrono::Utc::now(),
            status: "processing".to_string(),
            output_files: Vec::new(),
        }
    }
}
