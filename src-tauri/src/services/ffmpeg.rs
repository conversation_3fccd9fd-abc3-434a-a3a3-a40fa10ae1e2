use std::process::{Command, Stdio};
use std::path::Path;
use tokio::process::Command as TokioCommand;
use crate::models::{AudioFile, AudioQuality};
use anyhow::{Result, anyhow};
use tracing::{info, error, debug};

pub struct FFmpegService {
    ffmpeg_path: String,
}

impl FFmpegService {
    pub fn new() -> Result<Self> {
        let ffmpeg_path = Self::find_ffmpeg()?;
        Ok(Self { ffmpeg_path })
    }

    fn find_ffmpeg() -> Result<String> {
        // Try to find ffmpeg in PATH
        if let Ok(output) = Command::new("which").arg("ffmpeg").output() {
            if output.status.success() {
                let path = String::from_utf8(output.stdout)?;
                return Ok(path.trim().to_string());
            }
        }

        // Try common installation paths on macOS
        let common_paths = [
            "/usr/local/bin/ffmpeg",
            "/opt/homebrew/bin/ffmpeg",
            "/usr/bin/ffmpeg",
        ];

        for path in &common_paths {
            if Path::new(path).exists() {
                return Ok(path.to_string());
            }
        }

        Err(anyhow!("FFmpeg not found. Please install FFmpeg first."))
    }

    pub async fn extract_audio(
        &self,
        input_path: &str,
        output_path: &str,
        quality: AudioQuality,
    ) -> Result<AudioFile> {
        info!("Starting audio extraction from {} to {}", input_path, output_path);

        let bitrate = quality.to_bitrate();
        
        let mut cmd = TokioCommand::new(&self.ffmpeg_path);
        cmd.args([
            "-i", input_path,
            "-vn", // No video
            "-acodec", "mp3",
            "-ab", &format!("{}k", bitrate),
            "-ar", "44100", // Sample rate
            "-ac", "2", // Stereo
            "-y", // Overwrite output file
            output_path,
        ]);

        cmd.stdout(Stdio::piped());
        cmd.stderr(Stdio::piped());

        debug!("Executing FFmpeg command: {:?}", cmd);

        let output = cmd.output().await?;

        if !output.status.success() {
            let stderr = String::from_utf8_lossy(&output.stderr);
            error!("FFmpeg failed: {}", stderr);
            return Err(anyhow!("FFmpeg extraction failed: {}", stderr));
        }

        // Get file info
        let metadata = std::fs::metadata(output_path)?;
        let file_name = Path::new(output_path)
            .file_name()
            .unwrap()
            .to_string_lossy()
            .to_string();

        let mut audio_file = AudioFile::new(
            output_path.to_string(),
            file_name,
            metadata.len(),
        );

        // Get audio duration
        if let Ok(duration) = self.get_audio_duration(output_path).await {
            audio_file.duration = duration;
        }

        audio_file.bitrate = bitrate;

        info!("Audio extraction completed successfully");
        Ok(audio_file)
    }

    pub async fn get_video_info(&self, input_path: &str) -> Result<(f64, String)> {
        let mut cmd = TokioCommand::new(&self.ffmpeg_path);
        cmd.args([
            "-i", input_path,
            "-f", "null",
            "-",
        ]);

        cmd.stdout(Stdio::piped());
        cmd.stderr(Stdio::piped());

        let output = cmd.output().await?;
        let stderr = String::from_utf8_lossy(&output.stderr);

        // Parse duration from ffmpeg output
        let duration = self.parse_duration_from_output(&stderr)?;
        
        // Parse format info
        let format = self.parse_format_from_output(&stderr);

        Ok((duration, format))
    }

    async fn get_audio_duration(&self, audio_path: &str) -> Result<f64> {
        let mut cmd = TokioCommand::new(&self.ffmpeg_path);
        cmd.args([
            "-i", audio_path,
            "-f", "null",
            "-",
        ]);

        cmd.stdout(Stdio::piped());
        cmd.stderr(Stdio::piped());

        let output = cmd.output().await?;
        let stderr = String::from_utf8_lossy(&output.stderr);

        self.parse_duration_from_output(&stderr)
    }

    fn parse_duration_from_output(&self, output: &str) -> Result<f64> {
        for line in output.lines() {
            if line.contains("Duration:") {
                if let Some(duration_str) = line.split("Duration: ").nth(1) {
                    if let Some(time_part) = duration_str.split(',').next() {
                        return self.parse_time_to_seconds(time_part.trim());
                    }
                }
            }
        }
        Err(anyhow!("Could not parse duration from FFmpeg output"))
    }

    fn parse_format_from_output(&self, output: &str) -> String {
        for line in output.lines() {
            if line.contains("Input #0") {
                if let Some(format_part) = line.split(", ").nth(1) {
                    return format_part.split(',').next().unwrap_or("unknown").to_string();
                }
            }
        }
        "unknown".to_string()
    }

    fn parse_time_to_seconds(&self, time_str: &str) -> Result<f64> {
        let parts: Vec<&str> = time_str.split(':').collect();
        if parts.len() != 3 {
            return Err(anyhow!("Invalid time format"));
        }

        let hours: f64 = parts[0].parse()?;
        let minutes: f64 = parts[1].parse()?;
        let seconds: f64 = parts[2].parse()?;

        Ok(hours * 3600.0 + minutes * 60.0 + seconds)
    }

    pub fn is_available(&self) -> bool {
        Command::new(&self.ffmpeg_path)
            .arg("-version")
            .output()
            .map(|output| output.status.success())
            .unwrap_or(false)
    }
}
