use tauri::command;
use crate::models::{SubtitleData};
use crate::services::WhisperService;
use anyhow::Result;
use tracing::{info, error};

#[command]
pub async fn transcribe_audio(
    audio_path: String,
    language: String,
    model: String,
) -> Result<SubtitleData, String> {
    info!("Starting transcription of: {}", audio_path);
    
    let whisper = WhisperService::new()
        .map_err(|e| format!("Whisper not available: {}", e))?;
    
    match whisper.transcribe_audio(&audio_path, &language, &model).await {
        Ok(subtitle_data) => {
            info!("Transcription completed successfully");
            Ok(subtitle_data)
        }
        Err(e) => {
            error!("Transcription failed: {}", e);
            Err(format!("Transcription failed: {}", e))
        }
    }
}

#[command]
pub async fn get_whisper_models() -> Result<Vec<String>, String> {
    Ok(WhisperService::get_available_models()
        .iter()
        .map(|s| s.to_string())
        .collect())
}

#[command]
pub async fn get_supported_languages() -> Result<Vec<LanguageOption>, String> {
    Ok(WhisperService::get_supported_languages()
        .iter()
        .map(|(code, name)| LanguageOption {
            code: code.to_string(),
            name: name.to_string(),
        })
        .collect())
}

#[derive(serde::Serialize, serde::Deserialize)]
pub struct LanguageOption {
    pub code: String,
    pub name: String,
}
