use tauri::command;
use crate::models::UserSettings;
use crate::services::{ConfigService, ProcessingHistoryItem};
use anyhow::Result;
use tracing::{info, error};

#[command]
pub async fn save_user_settings(settings: UserSettings) -> Result<(), String> {
    info!("Saving user settings");
    
    let config_service = ConfigService::new()
        .map_err(|e| format!("Failed to create config service: {}", e))?;
    
    match config_service.save_settings(&settings).await {
        Ok(_) => {
            info!("Settings saved successfully");
            Ok(())
        }
        Err(e) => {
            error!("Failed to save settings: {}", e);
            Err(format!("Failed to save settings: {}", e))
        }
    }
}

#[command]
pub async fn load_user_settings() -> Result<UserSettings, String> {
    info!("Loading user settings");
    
    let config_service = ConfigService::new()
        .map_err(|e| format!("Failed to create config service: {}", e))?;
    
    match config_service.load_settings().await {
        Ok(settings) => {
            info!("Settings loaded successfully");
            Ok(settings)
        }
        Err(e) => {
            error!("Failed to load settings: {}", e);
            // Return default settings if loading fails
            Ok(UserSettings::default())
        }
    }
}

#[command]
pub async fn delete_api_key() -> Result<(), String> {
    info!("Deleting API key");
    
    let config_service = ConfigService::new()
        .map_err(|e| format!("Failed to create config service: {}", e))?;
    
    match config_service.delete_api_key().await {
        Ok(_) => {
            info!("API key deleted successfully");
            Ok(())
        }
        Err(e) => {
            error!("Failed to delete API key: {}", e);
            Err(format!("Failed to delete API key: {}", e))
        }
    }
}

#[command]
pub async fn get_processing_history() -> Result<Vec<ProcessingHistoryItem>, String> {
    info!("Loading processing history");
    
    let config_service = ConfigService::new()
        .map_err(|e| format!("Failed to create config service: {}", e))?;
    
    match config_service.load_processing_history().await {
        Ok(history) => {
            info!("Processing history loaded successfully");
            Ok(history)
        }
        Err(e) => {
            error!("Failed to load processing history: {}", e);
            Ok(Vec::new())
        }
    }
}

#[command]
pub async fn save_processing_history_item(history_item: ProcessingHistoryItem) -> Result<(), String> {
    info!("Saving processing history item");
    
    let config_service = ConfigService::new()
        .map_err(|e| format!("Failed to create config service: {}", e))?;
    
    match config_service.save_processing_history(&history_item).await {
        Ok(_) => {
            info!("Processing history item saved successfully");
            Ok(())
        }
        Err(e) => {
            error!("Failed to save processing history item: {}", e);
            Err(format!("Failed to save processing history item: {}", e))
        }
    }
}

#[command]
pub async fn get_app_directories() -> Result<AppDirectories, String> {
    let config_service = ConfigService::new()
        .map_err(|e| format!("Failed to create config service: {}", e))?;
    
    config_service.ensure_directories().await
        .map_err(|e| format!("Failed to ensure directories: {}", e))?;
    
    Ok(AppDirectories {
        temp_dir: config_service.get_temp_dir().to_string_lossy().to_string(),
        output_dir: config_service.get_output_dir().to_string_lossy().to_string(),
    })
}

#[derive(serde::Serialize, serde::Deserialize)]
pub struct AppDirectories {
    pub temp_dir: String,
    pub output_dir: String,
}
