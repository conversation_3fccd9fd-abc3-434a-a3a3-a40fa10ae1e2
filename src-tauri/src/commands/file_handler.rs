use tauri::command;
use crate::models::VideoFile;
use crate::services::FFmpegService;
use anyhow::Result;
use std::path::Path;
use tracing::{info, error};

#[command]
pub async fn select_video_file() -> Result<Option<VideoFile>, String> {
    info!("Opening file dialog for video selection");

    // This would be called from the frontend using the dialog plugin
    // For now, return None as this needs to be handled in the frontend
    Ok(None)
}

#[command]
pub async fn validate_video_file(path: String) -> Result<bool, String> {
    info!("Validating video file: {}", path);

    let file_path = Path::new(&path);

    // Check if file exists
    if !file_path.exists() {
        return Ok(false);
    }

    // Check file extension
    if let Some(extension) = file_path.extension() {
        let ext = extension.to_string_lossy().to_lowercase();
        let supported_formats = vec![
            "mp4", "mov", "avi", "mkv", "wmv", "flv", "webm", "m4v", "3gp"
        ];

        if !supported_formats.contains(&ext.as_str()) {
            return Ok(false);
        }
    } else {
        return Ok(false);
    }

    // Try to get video info using FFmpeg
    match FFmpegService::new() {
        Ok(ffmpeg) => {
            match ffmpeg.get_video_info(&path).await {
                Ok(_) => Ok(true),
                Err(e) => {
                    error!("Failed to validate video file: {}", e);
                    Ok(false)
                }
            }
        }
        Err(e) => {
            error!("FFmpeg not available: {}", e);
            Err(format!("FFmpeg not available: {}", e))
        }
    }
}

#[command]
pub async fn get_video_info(path: String) -> Result<VideoFile, String> {
    info!("Getting video info for: {}", path);

    let file_path = Path::new(&path);

    if !file_path.exists() {
        return Err("File does not exist".to_string());
    }

    let metadata = std::fs::metadata(&path)
        .map_err(|e| format!("Failed to read file metadata: {}", e))?;

    let file_name = file_path
        .file_name()
        .unwrap_or_default()
        .to_string_lossy()
        .to_string();

    let mut video_file = VideoFile::new(path.clone(), file_name, metadata.len());

    // Get video metadata using FFmpeg
    match FFmpegService::new() {
        Ok(ffmpeg) => {
            match ffmpeg.get_video_info(&path).await {
                Ok((duration, format)) => {
                    video_file.duration = Some(duration);
                    video_file.format = format;
                }
                Err(e) => {
                    error!("Failed to get video metadata: {}", e);
                    // Continue without metadata
                }
            }
        }
        Err(e) => {
            error!("FFmpeg not available: {}", e);
            return Err(format!("FFmpeg not available: {}", e));
        }
    }

    Ok(video_file)
}

#[command]
pub async fn get_supported_formats() -> Result<Vec<String>, String> {
    Ok(vec![
        "mp4".to_string(),
        "mov".to_string(),
        "avi".to_string(),
        "mkv".to_string(),
        "wmv".to_string(),
        "flv".to_string(),
        "webm".to_string(),
        "m4v".to_string(),
        "3gp".to_string(),
    ])
}

#[command]
pub async fn check_dependencies() -> Result<DependencyStatus, String> {
    info!("Checking system dependencies");

    let mut status = DependencyStatus {
        ffmpeg_available: false,
        whisper_available: false,
        ffmpeg_version: None,
        whisper_version: None,
    };

    // Check FFmpeg
    match FFmpegService::new() {
        Ok(ffmpeg) => {
            status.ffmpeg_available = ffmpeg.is_available();
            if status.ffmpeg_available {
                status.ffmpeg_version = Some("Available".to_string());
            }
        }
        Err(_) => {
            status.ffmpeg_available = false;
        }
    }

    // Check Whisper
    match crate::services::WhisperService::new() {
        Ok(whisper) => {
            status.whisper_available = whisper.is_available();
            if status.whisper_available {
                status.whisper_version = Some("Available".to_string());
            }
        }
        Err(_) => {
            status.whisper_available = false;
        }
    }

    Ok(status)
}

#[derive(serde::Serialize, serde::Deserialize)]
pub struct DependencyStatus {
    pub ffmpeg_available: bool,
    pub whisper_available: bool,
    pub ffmpeg_version: Option<String>,
    pub whisper_version: Option<String>,
}
