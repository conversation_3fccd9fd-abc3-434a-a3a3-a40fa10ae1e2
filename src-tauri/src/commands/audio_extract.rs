use tauri::command;
use crate::models::{AudioFile, AudioQuality};
use crate::services::{FFmpegService, ConfigService};
use anyhow::Result;
use std::path::Path;
use tracing::{info, error};

#[command]
pub async fn extract_audio_from_video(
    video_path: String,
    quality: String,
) -> Result<AudioFile, String> {
    info!("Starting audio extraction from: {}", video_path);
    
    // Parse quality
    let audio_quality = match quality.as_str() {
        "128" => AudioQuality::Low,
        "192" => AudioQuality::Medium,
        "320" => AudioQuality::High,
        _ => AudioQuality::Medium,
    };
    
    // Create FFmpeg service
    let ffmpeg = FFmpegService::new()
        .map_err(|e| format!("FFmpeg not available: {}", e))?;
    
    // Create config service to get temp directory
    let config_service = ConfigService::new()
        .map_err(|e| format!("Failed to create config service: {}", e))?;
    
    config_service.ensure_directories().await
        .map_err(|e| format!("Failed to create directories: {}", e))?;
    
    // Generate output path
    let video_path_obj = Path::new(&video_path);
    let video_stem = video_path_obj
        .file_stem()
        .unwrap_or_default()
        .to_string_lossy();
    
    let temp_dir = config_service.get_temp_dir();
    let output_path = temp_dir.join(format!("{}.mp3", video_stem));
    let output_path_str = output_path.to_string_lossy().to_string();
    
    // Extract audio
    match ffmpeg.extract_audio(&video_path, &output_path_str, audio_quality).await {
        Ok(audio_file) => {
            info!("Audio extraction completed successfully");
            Ok(audio_file)
        }
        Err(e) => {
            error!("Audio extraction failed: {}", e);
            Err(format!("Audio extraction failed: {}", e))
        }
    }
}

#[command]
pub async fn get_audio_quality_options() -> Result<Vec<AudioQualityOption>, String> {
    Ok(vec![
        AudioQualityOption {
            value: "128".to_string(),
            label: "Low (128 kbps)".to_string(),
            description: "Smaller file size, lower quality".to_string(),
        },
        AudioQualityOption {
            value: "192".to_string(),
            label: "Medium (192 kbps)".to_string(),
            description: "Balanced size and quality".to_string(),
        },
        AudioQualityOption {
            value: "320".to_string(),
            label: "High (320 kbps)".to_string(),
            description: "Larger file size, higher quality".to_string(),
        },
    ])
}

#[command]
pub async fn estimate_audio_size(
    video_path: String,
    quality: String,
) -> Result<AudioSizeEstimate, String> {
    info!("Estimating audio size for: {}", video_path);
    
    // Get video duration
    let ffmpeg = FFmpegService::new()
        .map_err(|e| format!("FFmpeg not available: {}", e))?;
    
    let (duration, _) = ffmpeg.get_video_info(&video_path).await
        .map_err(|e| format!("Failed to get video info: {}", e))?;
    
    // Parse quality
    let bitrate = match quality.as_str() {
        "128" => 128,
        "192" => 192,
        "320" => 320,
        _ => 192,
    };
    
    // Estimate file size (bitrate * duration / 8 for bytes)
    let estimated_size_bytes = (bitrate as f64 * duration * 1000.0 / 8.0) as u64;
    
    Ok(AudioSizeEstimate {
        estimated_size_bytes,
        estimated_size_mb: estimated_size_bytes as f64 / (1024.0 * 1024.0),
        duration_seconds: duration,
        bitrate_kbps: bitrate,
    })
}

#[command]
pub async fn cleanup_temp_audio_files() -> Result<(), String> {
    info!("Cleaning up temporary audio files");
    
    let config_service = ConfigService::new()
        .map_err(|e| format!("Failed to create config service: {}", e))?;
    
    config_service.cleanup_temp_files().await
        .map_err(|e| format!("Failed to cleanup temp files: {}", e))?;
    
    Ok(())
}

#[derive(serde::Serialize, serde::Deserialize)]
pub struct AudioQualityOption {
    pub value: String,
    pub label: String,
    pub description: String,
}

#[derive(serde::Serialize, serde::Deserialize)]
pub struct AudioSizeEstimate {
    pub estimated_size_bytes: u64,
    pub estimated_size_mb: f64,
    pub duration_seconds: f64,
    pub bitrate_kbps: u32,
}
