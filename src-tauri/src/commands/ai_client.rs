use tauri::command;
use crate::models::{AIConfig, AIContent, ContentType};
use crate::services::AIService;
use anyhow::Result;
use tracing::{info, error};

#[command]
pub async fn generate_ai_content(
    subtitle_text: String,
    content_type: String,
    ai_config: AIConfig,
) -> Result<AIContent, String> {
    info!("Generating AI content for type: {}", content_type);
    
    let ai_service = AIService::new();
    
    let content_type_enum = match content_type.as_str() {
        "seo" => ContentType::SEO,
        "youtube" => ContentType::YouTube,
        "cover" => ContentType::Cover,
        "all" => ContentType::All,
        _ => ContentType::All,
    };
    
    match ai_service.generate_content(&subtitle_text, content_type_enum, &ai_config).await {
        Ok(ai_content) => {
            info!("AI content generation completed successfully");
            Ok(ai_content)
        }
        Err(e) => {
            error!("AI content generation failed: {}", e);
            Err(format!("AI content generation failed: {}", e))
        }
    }
}

#[command]
pub async fn test_ai_connection(ai_config: AIConfig) -> Result<bool, String> {
    info!("Testing AI connection");
    
    let ai_service = AIService::new();
    
    match ai_service.test_connection(&ai_config).await {
        Ok(success) => {
            if success {
                info!("AI connection test successful");
            } else {
                info!("AI connection test failed");
            }
            Ok(success)
        }
        Err(e) => {
            error!("AI connection test error: {}", e);
            Err(format!("AI connection test error: {}", e))
        }
    }
}

#[command]
pub async fn get_content_types() -> Result<Vec<ContentTypeOption>, String> {
    Ok(vec![
        ContentTypeOption {
            value: "seo".to_string(),
            label: "SEO Content".to_string(),
            description: "Keywords, meta description, and title suggestions".to_string(),
        },
        ContentTypeOption {
            value: "youtube".to_string(),
            label: "YouTube Guide".to_string(),
            description: "Title, description, tags, and optimization tips".to_string(),
        },
        ContentTypeOption {
            value: "cover".to_string(),
            label: "Cover Suggestions".to_string(),
            description: "Thumbnail and cover design ideas".to_string(),
        },
        ContentTypeOption {
            value: "all".to_string(),
            label: "All Content".to_string(),
            description: "Generate all types of content".to_string(),
        },
    ])
}

#[derive(serde::Serialize, serde::Deserialize)]
pub struct ContentTypeOption {
    pub value: String,
    pub label: String,
    pub description: String,
}
