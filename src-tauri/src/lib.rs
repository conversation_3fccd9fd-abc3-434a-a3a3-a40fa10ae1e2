mod models;
mod services;
mod commands;

use tracing_subscriber;

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // Initialize logging
    tracing_subscriber::fmt::init();

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_shell::init())
        .invoke_handler(tauri::generate_handler![
            greet,
            commands::validate_video_file,
            commands::get_video_info,
            commands::get_supported_formats,
            commands::check_dependencies,
            commands::extract_audio_from_video,
            commands::get_audio_quality_options,
            commands::estimate_audio_size,
            commands::cleanup_temp_audio_files,
            commands::transcribe_audio,
            commands::get_whisper_models,
            commands::get_supported_languages,
            commands::generate_ai_content,
            commands::test_ai_connection,
            commands::get_content_types,
            commands::save_user_settings,
            commands::load_user_settings,
            commands::delete_api_key,
            commands::get_processing_history,
            commands::save_processing_history_item,
            commands::get_app_directories,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
