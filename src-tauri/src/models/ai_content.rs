use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AIContent {
    pub id: String,
    pub seo_content: SEOContent,
    pub youtube_guide: YouTubeGuide,
    pub cover_suggestions: Vec<CoverSuggestion>,
    pub created_at: DateTime<Utc>,
}

impl AIContent {
    pub fn new() -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            seo_content: SEOContent::default(),
            youtube_guide: YouTubeGuide::default(),
            cover_suggestions: Vec::new(),
            created_at: Utc::now(),
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, Default)]
pub struct SEOContent {
    pub keywords: Vec<String>,
    pub meta_description: String,
    pub title_suggestions: Vec<String>,
    pub content_summary: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct YouTubeGuide {
    pub title: String,
    pub description: String,
    pub tags: Vec<String>,
    pub thumbnail_tips: Vec<String>,
    pub best_posting_time: String,
    pub target_audience: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CoverSuggestion {
    pub title: String,
    pub description: String,
    pub style: String,
    pub color_scheme: Vec<String>,
    pub elements: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ContentType {
    SEO,
    YouTube,
    Cover,
    All,
}

impl ContentType {
    pub fn to_string(&self) -> &'static str {
        match self {
            ContentType::SEO => "seo",
            ContentType::YouTube => "youtube",
            ContentType::Cover => "cover",
            ContentType::All => "all",
        }
    }
}
