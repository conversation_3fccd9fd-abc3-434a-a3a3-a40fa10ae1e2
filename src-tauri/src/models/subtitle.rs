use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SubtitleData {
    pub id: String,
    pub entries: Vec<SubtitleEntry>,
    pub language: String,
    pub total_duration: f64,
    pub created_at: DateTime<Utc>,
}

impl SubtitleData {
    pub fn new(language: String) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            entries: Vec::new(),
            language,
            total_duration: 0.0,
            created_at: Utc::now(),
        }
    }

    pub fn to_srt(&self) -> String {
        let mut srt = String::new();
        for (index, entry) in self.entries.iter().enumerate() {
            srt.push_str(&format!("{}\n", index + 1));
            srt.push_str(&format!(
                "{} --> {}\n",
                format_time(entry.start_time),
                format_time(entry.end_time)
            ));
            srt.push_str(&format!("{}\n\n", entry.text));
        }
        srt
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct SubtitleEntry {
    pub index: u32,
    pub start_time: f64,
    pub end_time: f64,
    pub text: String,
}

impl SubtitleEntry {
    pub fn new(index: u32, start_time: f64, end_time: f64, text: String) -> Self {
        Self {
            index,
            start_time,
            end_time,
            text,
        }
    }
}

fn format_time(seconds: f64) -> String {
    let hours = (seconds / 3600.0) as u32;
    let minutes = ((seconds % 3600.0) / 60.0) as u32;
    let secs = (seconds % 60.0) as u32;
    let millis = ((seconds % 1.0) * 1000.0) as u32;
    
    format!("{:02}:{:02}:{:02},{:03}", hours, minutes, secs, millis)
}
