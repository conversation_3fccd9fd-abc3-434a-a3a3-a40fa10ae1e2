use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AudioFile {
    pub id: String,
    pub path: String,
    pub name: String,
    pub size: u64,
    pub duration: f64,
    pub sample_rate: u32,
    pub bitrate: u32,
    pub channels: u32,
    pub created_at: DateTime<Utc>,
}

impl AudioFile {
    pub fn new(path: String, name: String, size: u64) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            path,
            name,
            size,
            duration: 0.0,
            sample_rate: 44100,
            bitrate: 192,
            channels: 2,
            created_at: Utc::now(),
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum AudioQuality {
    Low,    // 128kbps
    Medium, // 192kbps
    High,   // 320kbps
}

impl AudioQuality {
    pub fn to_bitrate(&self) -> u32 {
        match self {
            AudioQuality::Low => 128,
            AudioQuality::Medium => 192,
            AudioQuality::High => 320,
        }
    }
}
