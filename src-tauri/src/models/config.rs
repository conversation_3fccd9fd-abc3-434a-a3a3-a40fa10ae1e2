use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>, Deserialize)]
pub struct UserSettings {
    pub ai_config: AIConfig,
    pub audio_quality: String,
    pub language: String,
    pub theme: String,
    pub output_directory: String,
}

impl Default for UserSettings {
    fn default() -> Self {
        Self {
            ai_config: AIConfig::default(),
            audio_quality: "192".to_string(),
            language: "auto".to_string(),
            theme: "light".to_string(),
            output_directory: "".to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIConfig {
    pub base_url: String,
    pub api_key: String,
    pub model_name: String,
    pub temperature: f32,
    pub max_tokens: u32,
}

impl Default for AIConfig {
    fn default() -> Self {
        Self {
            base_url: "https://api.openai.com/v1".to_string(),
            api_key: "".to_string(),
            model_name: "gpt-4o".to_string(),
            temperature: 0.7,
            max_tokens: 2048,
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProcessingStatus {
    pub stage: ProcessingStage,
    pub progress: f32,
    pub message: String,
    pub current_file: Option<String>,
}

impl Default for ProcessingStatus {
    fn default() -> Self {
        Self {
            stage: ProcessingStage::Idle,
            progress: 0.0,
            message: "Ready".to_string(),
            current_file: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ProcessingStage {
    Idle,
    Validating,
    Extracting,
    Transcribing,
    Generating,
    Complete,
    Error,
}

impl ProcessingStage {
    pub fn to_string(&self) -> &'static str {
        match self {
            ProcessingStage::Idle => "idle",
            ProcessingStage::Validating => "validating",
            ProcessingStage::Extracting => "extracting",
            ProcessingStage::Transcribing => "transcribing",
            ProcessingStage::Generating => "generating",
            ProcessingStage::Complete => "complete",
            ProcessingStage::Error => "error",
        }
    }
}
