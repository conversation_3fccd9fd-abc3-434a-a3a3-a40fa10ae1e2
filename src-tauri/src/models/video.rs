use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

#[derive(<PERSON>bug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VideoFile {
    pub id: String,
    pub path: String,
    pub name: String,
    pub size: u64,
    pub duration: Option<f64>,
    pub format: String,
    pub created_at: DateTime<Utc>,
}

impl VideoFile {
    pub fn new(path: String, name: String, size: u64) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            path,
            name,
            size,
            duration: None,
            format: String::new(),
            created_at: Utc::now(),
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct VideoMetadata {
    pub duration: f64,
    pub width: u32,
    pub height: u32,
    pub fps: f64,
    pub bitrate: u64,
    pub codec: String,
}
